"""
Trading strategy templates for backtesting

All specific strategy implementations have been removed as they were unprofitable.
Use the template functions in basic.py, basic_pandas.py, and custom.py to implement
your own profitable strategies.

Available strategies:
- rsi_martingale: RSI-based martingale strategy with pyramiding (10% → 20% → 70%)
"""

from .rsi_martingale import rsi_martingale_strategy

__all__ = ["rsi_martingale_strategy"]
