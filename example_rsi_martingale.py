#!/usr/bin/env python3
"""
Example usage of RSI Martingale Strategy

This script demonstrates how to use the RSI martingale strategy with sample data.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import sys
from pathlib import Path

# Add quanttrade to path
sys.path.append(str(Path(__file__).parent))

from quanttrade.strategies.rsi_martingale import rsi_martingale_strategy


def generate_sample_data(periods: int = 1000, start_price: float = 50000.0) -> pd.DataFrame:
    """
    Generate sample OHLCV data for testing.
    
    Args:
        periods: Number of periods to generate
        start_price: Starting price
        
    Returns:
        DataFrame with sample OHLCV data
    """
    np.random.seed(42)  # For reproducible results
    
    # Generate timestamps (hourly data)
    start_time = datetime.now() - timedelta(hours=periods)
    timestamps = [start_time + timedelta(hours=i) for i in range(periods)]
    
    # Generate price data with some trending behavior
    returns = np.random.normal(0, 0.02, periods)  # 2% volatility
    
    # Add some trending periods
    trend_periods = periods // 10
    for i in range(0, periods, trend_periods):
        trend_direction = np.random.choice([-1, 1])
        trend_strength = np.random.uniform(0.001, 0.005)
        end_idx = min(i + trend_periods, periods)
        returns[i:end_idx] += trend_direction * trend_strength
    
    # Calculate prices
    prices = [start_price]
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    # Generate OHLC from close prices
    df = pd.DataFrame({
        'Timestamp': timestamps,
        'Close': prices
    })
    
    # Generate OHLC data
    df['Open'] = df['Close'].shift(1).fillna(df['Close'].iloc[0])
    df['High'] = df[['Open', 'Close']].max(axis=1) * (1 + np.random.uniform(0, 0.01, len(df)))
    df['Low'] = df[['Open', 'Close']].min(axis=1) * (1 - np.random.uniform(0, 0.01, len(df)))
    df['Volume'] = np.random.uniform(100, 1000, len(df))
    
    return df


def run_example():
    """Run example backtest with sample data."""
    print("=" * 60)
    print("RSI Martingale Strategy Example")
    print("=" * 60)
    
    # Generate sample data
    print("Generating sample data...")
    df = generate_sample_data(periods=2000, start_price=45000.0)
    print(f"Generated {len(df)} periods of hourly data")
    
    # Strategy parameters
    strategy_params = {
        'rsi_period': 14,
        'rsi_oversold': 30,
        'rsi_overbought': 70,
        'rsi_middle': 50,
        'initial_capital': 10000.0,
        'trading_fee_rate': 0.0005  # 0.05%
    }
    
    print(f"Running strategy with parameters:")
    for key, value in strategy_params.items():
        print(f"  {key}: {value}")
    
    # Run strategy
    print("\nExecuting RSI Martingale strategy...")
    df_result = rsi_martingale_strategy(df, **strategy_params)
    
    # Calculate basic metrics
    initial_capital = strategy_params['initial_capital']
    final_equity = df_result['equity_curve'].iloc[-1]
    total_return = (final_equity / initial_capital - 1) * 100
    
    # Count trades (signal changes)
    signals = df_result['signal']
    trade_count = (signals != 0).sum()
    
    # Count pyramid levels
    max_pyramid = df_result['pyramid_level'].max()
    
    # Print results
    print("\n" + "=" * 60)
    print("RESULTS SUMMARY")
    print("=" * 60)
    print(f"Initial Capital:      ${initial_capital:,.2f}")
    print(f"Final Equity:         ${final_equity:,.2f}")
    print(f"Total Return:         {total_return:.2f}%")
    print(f"Total Signals:        {trade_count}")
    print(f"Max Pyramid Level:    {max_pyramid}")
    
    # Show some sample trades
    trade_signals = df_result[df_result['signal'] != 0][['Timestamp', 'Close', 'RSI', 'signal', 'position_side', 'pyramid_level', 'equity_curve']].head(10)
    if len(trade_signals) > 0:
        print(f"\nFirst 10 Trade Signals:")
        print(trade_signals.to_string(index=False))
    
    # Create simple plot
    print("\nGenerating plot...")
    fig, axes = plt.subplots(3, 1, figsize=(12, 10))
    
    # Plot 1: Price and RSI
    ax1 = axes[0]
    ax1.plot(df_result['Timestamp'], df_result['Close'], label='Price', color='black', linewidth=1)
    ax1.set_ylabel('Price ($)')
    ax1.set_title('Price and RSI')
    
    ax1_twin = ax1.twinx()
    ax1_twin.plot(df_result['Timestamp'], df_result['RSI'], label='RSI', color='purple', alpha=0.7)
    ax1_twin.axhline(y=30, color='green', linestyle='--', alpha=0.5)
    ax1_twin.axhline(y=70, color='red', linestyle='--', alpha=0.5)
    ax1_twin.axhline(y=50, color='blue', linestyle='--', alpha=0.5)
    ax1_twin.set_ylabel('RSI')
    ax1_twin.set_ylim(0, 100)
    
    # Plot 2: Position and Pyramid Level
    ax2 = axes[1]
    ax2.plot(df_result['Timestamp'], df_result['position_side'], label='Position Side', linewidth=2)
    ax2.plot(df_result['Timestamp'], df_result['pyramid_level'], label='Pyramid Level', linewidth=2)
    ax2.set_ylabel('Level')
    ax2.set_title('Position and Pyramid Tracking')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # Plot 3: Equity Curve
    ax3 = axes[2]
    ax3.plot(df_result['Timestamp'], df_result['equity_curve'], label='Equity Curve', color='green', linewidth=2)
    ax3.axhline(y=initial_capital, color='red', linestyle='--', alpha=0.5, label='Initial Capital')
    ax3.set_ylabel('Equity ($)')
    ax3.set_xlabel('Time')
    ax3.set_title('Equity Curve')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('rsi_martingale_example.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("Plot saved as 'rsi_martingale_example.png'")
    print("\nExample completed successfully!")
    
    return df_result


if __name__ == "__main__":
    result_df = run_example()
