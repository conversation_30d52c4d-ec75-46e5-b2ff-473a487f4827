import time, math, os, requests
from binance.um_futures import UMFutures
from dotenv import load_dotenv
from datetime import datetime
from functools import lru_cache
import os

load_dotenv()  # this reads .env in cwd

BASE = "https://fapi.binance.com"


# ── Binance helpers ────────────────────────────────────────────────────────
def binance_client() -> UMFutures:
    return UMFutures(
        key=os.getenv("BINANCE_API_KEY"), secret=os.getenv("BINANCE_SECRET_KEY"), base_url=BASE
    )


def server_offset_sec() -> float:
    srv_ms = requests.get(f"{BASE}/fapi/v1/time", timeout=3).json()["serverTime"]
    return time.time() - srv_ms / 1000


def mark_price(cli: UMFutures, sym: str) -> float:
    return float(cli.ticker_price(sym)["price"])


def last_hour_close(client, symbol):
    bars = client.klines(symbol=symbol, interval="1h", limit=2)
    close_price = float(bars[-2][4])
    return close_price


def oi_value(cli: UMFutures, sym: str) -> float:
    data = cli.open_interest_hist(sym, period="1h", limit=1)[0]
    return float(data["sumOpenInterestValue"])


def wallet_equity(cli: UMFutures) -> float:
    """Total wallet balance in USDT (includes unrealised PnL)."""
    acc = cli.account()  # GET /fapi/v2/account
    return float(acc["totalWalletBalance"])


def get_position_size(cli, symbol: str) -> float:
    """
    Returns the current net position amount for `symbol` via get_position_risk.
    Positive = long size, negative = short size, zero = flat.
    """
    resp = cli.get_position_risk(symbol=symbol)
    if not resp:
        return 0.0
    # In One‐Way mode resp[0]['positionSide']=="BOTH", positionAmt is net
    amt = float(resp[0]["positionAmt"])
    return amt


def _latest_realised_pnl(cli, symbol: str, since_ms: int) -> float:
    """
    First REALIZED_PNL entry after `since_ms` (epoch‑ms),
    using the connector’s get_income_history method.
    """
    for _ in range(6):  # retry for ~1.5 s
        rows = cli.get_income_history(
            symbol=symbol,
            incomeType="REALIZED_PNL",
            startTime=since_ms,
            limit=1,
        )
        if rows:
            # 'income' is a string like "-0.********"
            return float(rows[0]["income"])
        time.sleep(0.25)
    return 0.0


def close_position(cli, symbol: str) -> str:
    """
    Flattens any open position in symbol, computes realized PnL,
    and returns a summary string. Does NOT send any Telegram message.
    """
    # 1) fetch current position
    pos = cli.get_position_risk(symbol=symbol)
    if not pos:
        return f"{symbol}: no open position to close."

    pos = pos[0]
    amt = float(pos["positionAmt"])
    entry_price = float(pos["entryPrice"])

    if amt == 0:
        return f"{symbol}: position already flat."


    # 2) send a reduceOnly market order to flatten
    side = "SELL" if amt > 0 else "BUY"
    qty = abs(amt)
    order = cli.new_order(symbol=symbol, side=side, type="MARKET", quantity=qty, reduceOnly=True)

    # ---------- 3. avg close price ----------
    fills = order.get("fills") or []
    if fills:
        v = sum(float(f["price"]) * float(f["qty"]) for f in fills)
        q = sum(float(f["qty"]) for f in fills)
        avg_close = v / q
    else:  # futures response
        exec_qty = float(order.get("executedQty", 0))
        avg_close = (
            float(order["cummulativeQuoteQty"]) / exec_qty
            if exec_qty
            else float(pos["markPrice"])
        )

    # ---------- 4. realised PnL ----------
    t0 = int(time.time() * 1000)  # current time in ms

    pnl = _latest_realised_pnl(cli, symbol, since_ms=t0)

    # 5) prepare summary text
    txt = (
        f"*{datetime.utcnow():%Y-%m-%d %H:%M}*  {symbol} closed\n"
        f"Entry Price: {entry_price:.6f}\n"
        f"Exit  Price: {avg_close:.6f}\n"
        f"Size        : {qty}\n"
        f"PnL         : ${pnl:.2f}"
    )
    return txt


def get_current_position(client, symbol):
    """
    Returns 1 for a net long, -1 for a net short, or 0 for flat,
    based on USD-M futures positionAmt from /fapi/v3/positionRisk.
    """
    amt = get_position_size(client, symbol)
    if amt > 0:
        return 1
    elif amt < 0:
        return -1
    else:
        return 0


# ── Telegram helper ────────────────────────────────────────────────────────
def tg_send(text: str) -> None:
    url = f"https://api.telegram.org/bot{os.getenv('TG_BOT_TOKEN')}/sendMessage"
    requests.post(
        url, json={"chat_id": os.getenv("TG_CHAT_ID"), "text": text, "parse_mode": "Markdown"}
    )


@lru_cache(maxsize=1)
def _exchange_info(cli):
    """Download the full exchangeInfo only once per run."""
    return cli.exchange_info()["symbols"]  # list[dict]


def lot_step(cli, sym: str) -> float:
    """
    Return the allowed stepSize for `sym`, e.g. 0.001 for BTCUSDT.
    Cached for the whole run via _exchange_info().
    """
    for s in _exchange_info(cli):
        if s["symbol"] == sym:
            for f in s["filters"]:
                if f["filterType"] == "LOT_SIZE":
                    return float(f["stepSize"])
    raise ValueError(f"LOT_SIZE filter not found for {sym}")
