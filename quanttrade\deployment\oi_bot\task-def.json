{"family": "trading-bot", "cpu": "256", "memory": "512", "runtimePlatform": {"cpuArchitecture": "ARM64"}, "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "executionRoleArn": "arn:aws:iam::650251720729:role/ecsTaskExecutionRole", "containerDefinitions": [{"name": "bot", "image": "650251720729.dkr.ecr.ap-southeast-1.amazonaws.com/trading-bot:latest", "essential": true, "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/trading-bot", "awslogs-region": "ap-southeast-1", "awslogs-stream-prefix": "ecs"}}, "secrets": [{"name": "BINANCE_API_KEY", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:650251720729:secret:trading-bot/binance:BINANCE_API_KEY::"}, {"name": "BINANCE_SECRET_KEY", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:650251720729:secret:trading-bot/binance:BINANCE_SECRET_KEY::"}, {"name": "TG_BOT_TOKEN", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:650251720729:secret:trading-bot/telegram:TG_BOT_TOKEN::"}, {"name": "TG_CHAT_ID", "valueFrom": "arn:aws:secretsmanager:ap-southeast-1:650251720729:secret:trading-bot/telegram:TG_CHAT_ID::"}]}]}