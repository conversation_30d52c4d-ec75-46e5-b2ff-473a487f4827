#!/usr/bin/env python3
"""
Test RSI Martingale Strategy on BTC 4-Hour Data

This script tests the RSI martingale strategy specifically on your BTC 1-minute data
resampled to 4-hour timeframes.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
import sys
from datetime import datetime
from pathlib import Path

# Add quanttrade to path
sys.path.append(str(Path(__file__).parent))

from quanttrade.strategies.rsi_martingale import rsi_martingale_strategy
from quanttrade.data.loader import load_crypto_data, resample_ohlcv


def load_and_resample_btc_data(
    file_path: str = "quanttrade/data/datasets/btcusd_1_min_data.csv",
    start_date: str = "2022-01-01",
    end_date: str = "2024-12-31"
) -> pd.DataFrame:
    """
    Load BTC 1-minute data and resample to 4-hour timeframe.
    
    Args:
        file_path: Path to BTC CSV file
        start_date: Start date for analysis
        end_date: End date for analysis
        
    Returns:
        DataFrame with 4-hour OHLCV data
    """
    print("Loading BTC 1-minute data...")
    
    # Load the data
    df = load_crypto_data(file_path, start_date=start_date, end_date=end_date)
    
    print(f"Loaded {len(df)} 1-minute records")
    print(f"Date range: {df['Timestamp'].min()} to {df['Timestamp'].max()}")
    
    # Resample to 4-hour timeframe
    print("Resampling to 4-hour timeframe...")
    df_4h = resample_ohlcv(df, timeframe="4h")
    
    print(f"Resampled to {len(df_4h)} 4-hour records")
    return df_4h


def run_btc_4h_test():
    """Run RSI martingale strategy test on BTC 4-hour data."""
    
    print("=" * 70)
    print("BTC 4-Hour RSI Martingale Strategy Test")
    print("=" * 70)
    
    # Load and prepare data
    try:
        df = load_and_resample_btc_data()
    except Exception as e:
        print(f"Error loading data: {e}")
        print("Please ensure the BTC data file exists at: quanttrade/data/datasets/btcusd_1_min_data.csv")
        return None
    
    # Strategy parameters optimized for 4-hour timeframe
    strategy_params = {
        'rsi_period': 14,           # Standard RSI period
        'rsi_oversold': 30,         # Oversold threshold
        'rsi_overbought': 70,       # Overbought threshold
        'rsi_middle': 50,           # Middle reversion level
        'initial_capital': 10000.0, # Starting capital
        'trading_fee_rate': 0.0005  # 0.05% trading fee
    }
    
    print(f"\nStrategy Parameters:")
    for key, value in strategy_params.items():
        print(f"  {key}: {value}")
    
    print(f"\nData Summary:")
    print(f"  Records: {len(df)}")
    print(f"  Timeframe: 4-hour")
    print(f"  Date Range: {df['Timestamp'].min()} to {df['Timestamp'].max()}")
    print(f"  Price Range: ${df['Close'].min():.2f} - ${df['Close'].max():.2f}")
    
    # Run strategy
    print("\nExecuting RSI Martingale strategy...")
    df_result = rsi_martingale_strategy(df, **strategy_params)
    
    # Calculate performance metrics
    initial_capital = strategy_params['initial_capital']
    final_equity = df_result['equity_curve'].iloc[-1]
    total_return = (final_equity / initial_capital - 1) * 100
    
    # Calculate additional metrics
    equity_curve = df_result['equity_curve']
    returns = equity_curve.pct_change().dropna()
    
    # Annualized metrics for 4-hour data
    periods_per_year = 365 * 6  # 6 four-hour periods per day
    if len(returns) > 0:
        annualized_return = ((final_equity / initial_capital) ** (periods_per_year / len(equity_curve)) - 1) * 100
        sharpe_ratio = np.sqrt(periods_per_year) * returns.mean() / returns.std() if returns.std() > 0 else 0
    else:
        annualized_return = 0
        sharpe_ratio = 0
    
    # Maximum drawdown
    rolling_max = equity_curve.expanding().max()
    drawdown = (equity_curve - rolling_max) / rolling_max
    max_drawdown = drawdown.min() * 100
    
    # Trade analysis
    signals = df_result['signal']
    total_signals = (signals != 0).sum()
    long_signals = (signals == 1).sum()
    short_signals = (signals == -1).sum()
    max_pyramid = df_result['pyramid_level'].max()
    
    # Position analysis
    long_periods = (df_result['position_side'] == 1).sum()
    short_periods = (df_result['position_side'] == -1).sum()
    flat_periods = (df_result['position_side'] == 0).sum()
    
    # Print comprehensive results
    print("\n" + "=" * 70)
    print("BACKTEST RESULTS")
    print("=" * 70)
    
    print(f"\n📊 PERFORMANCE METRICS")
    print(f"Initial Capital:      ${initial_capital:,.2f}")
    print(f"Final Equity:         ${final_equity:,.2f}")
    print(f"Total Return:         {total_return:.2f}%")
    print(f"Annualized Return:    {annualized_return:.2f}%")
    print(f"Sharpe Ratio:         {sharpe_ratio:.2f}")
    print(f"Maximum Drawdown:     {max_drawdown:.2f}%")
    
    print(f"\n📈 TRADING ACTIVITY")
    print(f"Total Signals:        {total_signals}")
    print(f"Long Entries:         {long_signals}")
    print(f"Short Entries:        {short_signals}")
    print(f"Max Pyramid Level:    {max_pyramid}")
    
    print(f"\n⏱️ TIME IN MARKET")
    print(f"Long Periods:         {long_periods} ({long_periods/len(df)*100:.1f}%)")
    print(f"Short Periods:        {short_periods} ({short_periods/len(df)*100:.1f}%)")
    print(f"Flat Periods:         {flat_periods} ({flat_periods/len(df)*100:.1f}%)")
    
    # Show sample trades
    trade_signals = df_result[df_result['signal'] != 0][
        ['Timestamp', 'Close', 'RSI', 'signal', 'position_side', 'pyramid_level', 'equity_curve']
    ].head(15)
    
    if len(trade_signals) > 0:
        print(f"\n📋 FIRST 15 TRADE SIGNALS:")
        print(trade_signals.to_string(index=False, float_format='%.2f'))
    
    # Create comprehensive visualization
    print("\n📊 Generating analysis charts...")
    create_btc_4h_analysis_plots(df_result)
    
    # Save results
    output_dir = "backtest_results/btc_4h_rsi_martingale"
    os.makedirs(output_dir, exist_ok=True)
    
    # Save detailed results
    results_path = f"{output_dir}/btc_4h_rsi_martingale_results.csv"
    df_result.to_csv(results_path, index=False)
    print(f"Detailed results saved to: {results_path}")
    
    # Save equity curve
    equity_path = f"{output_dir}/btc_4h_equity_curve.csv"
    equity_curve.to_csv(equity_path, header=['equity'])
    print(f"Equity curve saved to: {equity_path}")
    
    print(f"\nAll files saved to: {output_dir}/")
    print("\n✅ BTC 4-Hour RSI Martingale test completed successfully!")
    
    return df_result


def create_btc_4h_analysis_plots(df: pd.DataFrame):
    """Create comprehensive analysis plots for BTC 4-hour data."""
    
    fig, axes = plt.subplots(5, 1, figsize=(16, 20))
    
    # Plot 1: Price and RSI with signals
    ax1 = axes[0]
    ax1.plot(df['Timestamp'], df['Close'], label='BTC Price', color='black', linewidth=1.5)
    
    # Mark trade signals
    buy_signals = df[df['signal'] == 1]
    sell_signals = df[df['signal'] == -1]
    
    if len(buy_signals) > 0:
        ax1.scatter(buy_signals['Timestamp'], buy_signals['Close'], 
                   color='green', marker='^', s=50, label='Buy Signal', alpha=0.7)
    if len(sell_signals) > 0:
        ax1.scatter(sell_signals['Timestamp'], sell_signals['Close'], 
                   color='red', marker='v', s=50, label='Sell Signal', alpha=0.7)
    
    ax1.set_ylabel('BTC Price ($)', fontsize=12)
    ax1.set_title('BTC 4-Hour Price with RSI Martingale Signals', fontsize=14, fontweight='bold')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Add RSI on secondary y-axis
    ax1_twin = ax1.twinx()
    ax1_twin.plot(df['Timestamp'], df['RSI'], label='RSI', color='purple', alpha=0.7, linewidth=1)
    ax1_twin.axhline(y=30, color='green', linestyle='--', alpha=0.5, label='Oversold (30)')
    ax1_twin.axhline(y=70, color='red', linestyle='--', alpha=0.5, label='Overbought (70)')
    ax1_twin.axhline(y=50, color='blue', linestyle='--', alpha=0.5, label='Middle (50)')
    ax1_twin.set_ylabel('RSI', color='purple', fontsize=12)
    ax1_twin.set_ylim(0, 100)
    ax1_twin.legend(loc='upper right')
    
    # Plot 2: Position and Pyramid Tracking
    ax2 = axes[1]
    ax2.plot(df['Timestamp'], df['position_side'], label='Position Side', color='blue', linewidth=2)
    ax2.plot(df['Timestamp'], df['pyramid_level'], label='Pyramid Level', color='orange', linewidth=2)
    ax2.set_ylabel('Level', fontsize=12)
    ax2.set_title('Position and Pyramid Level Tracking', fontsize=14, fontweight='bold')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(-1.5, 3.5)
    
    # Plot 3: Equity Curve
    ax3 = axes[2]
    ax3.plot(df['Timestamp'], df['equity_curve'], label='Equity Curve', color='green', linewidth=2)
    ax3.axhline(y=df['equity_curve'].iloc[0], color='red', linestyle='--', alpha=0.5, label='Initial Capital')
    ax3.set_ylabel('Equity ($)', fontsize=12)
    ax3.set_title('Portfolio Equity Curve', fontsize=14, fontweight='bold')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # Plot 4: Cash and Position Size
    ax4 = axes[3]
    ax4.plot(df['Timestamp'], df['cash'], label='Available Cash', color='blue', linewidth=1.5)
    ax4_twin = ax4.twinx()
    ax4_twin.plot(df['Timestamp'], df['position_size'], label='Position Size', color='red', linewidth=1.5)
    ax4.set_ylabel('Cash ($)', color='blue', fontsize=12)
    ax4_twin.set_ylabel('Position Size (BTC)', color='red', fontsize=12)
    ax4.set_title('Cash and Position Size Management', fontsize=14, fontweight='bold')
    ax4.legend(loc='upper left')
    ax4_twin.legend(loc='upper right')
    ax4.grid(True, alpha=0.3)
    
    # Plot 5: Unrealized PnL
    ax5 = axes[4]
    ax5.plot(df['Timestamp'], df['unrealized_pnl'], label='Unrealized PnL', color='purple', linewidth=1.5)
    ax5.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    ax5.fill_between(df['Timestamp'], df['unrealized_pnl'], 0, 
                     where=(df['unrealized_pnl'] >= 0), color='green', alpha=0.3, label='Profit')
    ax5.fill_between(df['Timestamp'], df['unrealized_pnl'], 0, 
                     where=(df['unrealized_pnl'] < 0), color='red', alpha=0.3, label='Loss')
    ax5.set_ylabel('Unrealized PnL ($)', fontsize=12)
    ax5.set_xlabel('Time', fontsize=12)
    ax5.set_title('Unrealized Profit & Loss', fontsize=14, fontweight='bold')
    ax5.legend()
    ax5.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # Save plot
    output_dir = "backtest_results/btc_4h_rsi_martingale"
    os.makedirs(output_dir, exist_ok=True)
    plot_path = f"{output_dir}/btc_4h_rsi_martingale_analysis.png"
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    print(f"Analysis plots saved to: {plot_path}")
    
    plt.show()


if __name__ == "__main__":
    result_df = run_btc_4h_test()
