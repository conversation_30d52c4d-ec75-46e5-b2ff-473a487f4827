"""
RSI-Based Martingale Strategy with Pyramiding

This strategy implements a sophisticated RSI-based martingale approach with pyramiding:
- Long positions: Enter at RSI ≤ 30, pyramid on reversions, exit at RSI ≥ 70
- Short positions: Enter at RSI ≥ 70, pyramid on reversions, exit at RSI ≤ 30
- Position sizing: 10% → 20% → 70% of available equity
- Mark-to-market equity tracking with 0.05% trading fees
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional


def calculate_rsi_pandas(prices: pd.Series, period: int = 14) -> pd.Series:
    """
    Calculate RSI using pandas.

    Args:
        prices: Price series (typically Close prices)
        period: RSI period (default 14)

    Returns:
        RSI values as pandas Series
    """
    delta = prices.diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)

    # Use exponential moving average for smoother RSI
    avg_gain = gain.ewm(span=period, adjust=False).mean()
    avg_loss = loss.ewm(span=period, adjust=False).mean()

    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    return rsi


class RSIMartingaleState:
    """Track the state of the RSI martingale strategy."""

    def __init__(self):
        self.position_side = 0  # 0: no position, 1: long, -1: short
        self.position_size = 0.0  # Total position size in base currency
        self.average_entry_price = 0.0
        self.cash = 0.0  # Available cash
        self.total_equity = 0.0  # Total equity (cash + position value)

        # Pyramiding tracking
        self.pyramid_level = 0  # 0: no position, 1: first entry, 2: second entry, 3: third entry
        self.last_rsi_state = None  # Track RSI state transitions
        self.waiting_for_reversion = False  # Waiting for RSI to hit 50 and come back

        # Trade tracking
        self.entry_prices = []  # List of entry prices for averaging
        self.entry_sizes = []  # List of position sizes for each entry

    def reset_position(self):
        """Reset position state."""
        self.position_side = 0
        self.position_size = 0.0
        self.average_entry_price = 0.0
        self.pyramid_level = 0
        self.last_rsi_state = None
        self.waiting_for_reversion = False
        self.entry_prices = []
        self.entry_sizes = []

    def add_to_position(self, price: float, size: float):
        """Add to existing position and update average entry price."""
        if self.position_size == 0:
            # First entry
            self.average_entry_price = price
            self.position_size = size
            self.entry_prices = [price]
            self.entry_sizes = [size]
        else:
            # Additional entry - calculate new average
            total_cost = sum(p * s for p, s in zip(self.entry_prices, self.entry_sizes))
            total_cost += price * size
            self.position_size += size
            self.average_entry_price = total_cost / self.position_size
            self.entry_prices.append(price)
            self.entry_sizes.append(size)


def rsi_martingale_strategy(df: pd.DataFrame, **kwargs) -> pd.DataFrame:
    """
    Implement RSI-based martingale strategy with pyramiding.

    Args:
        df: DataFrame with OHLCV data
        **kwargs: Strategy parameters

    Returns:
        DataFrame with strategy signals and state tracking
    """
    # Strategy parameters
    rsi_period = kwargs.get("rsi_period", 14)
    rsi_oversold = kwargs.get("rsi_oversold", 30)
    rsi_overbought = kwargs.get("rsi_overbought", 70)
    rsi_middle = kwargs.get("rsi_middle", 50)
    initial_capital = kwargs.get("initial_capital", 10000.0)
    trading_fee_rate = kwargs.get("trading_fee_rate", 0.0005)  # 0.05%

    # Make a copy to avoid modifying the original DataFrame
    df = df.copy()

    # Calculate RSI
    df["RSI"] = calculate_rsi_pandas(df["Close"], period=rsi_period)

    # Initialize strategy state
    state = RSIMartingaleState()
    state.cash = initial_capital
    state.total_equity = initial_capital

    # Initialize tracking columns
    df["signal"] = 0
    df["position_side"] = 0
    df["position_size"] = 0.0
    df["average_entry_price"] = 0.0
    df["cash"] = 0.0
    df["total_equity"] = 0.0
    df["pyramid_level"] = 0
    df["unrealized_pnl"] = 0.0
    df["equity_curve"] = 0.0

    # Iterate through data to implement strategy logic
    for i in range(1, len(df)):
        current_rsi = df["RSI"].iloc[i]
        prev_rsi = df["RSI"].iloc[i - 1]
        current_price = df["Close"].iloc[i]

        # Skip if RSI is NaN
        if pd.isna(current_rsi) or pd.isna(prev_rsi):
            continue

        # Update equity with mark-to-market
        if state.position_size != 0:
            # Calculate unrealized PnL
            price_diff = current_price - state.average_entry_price
            unrealized_pnl = state.position_side * price_diff * state.position_size

            # Account for potential exit fees
            exit_fee = current_price * state.position_size * trading_fee_rate
            unrealized_pnl -= exit_fee

            state.total_equity = (
                state.cash
                + (state.position_size * current_price * state.position_side)
                - exit_fee
            )
            df.loc[df.index[i], "unrealized_pnl"] = unrealized_pnl
        else:
            state.total_equity = state.cash
            df.loc[df.index[i], "unrealized_pnl"] = 0.0

        # Strategy logic
        signal = 0

        # Long side logic
        if state.position_side >= 0:  # No position or long position
            if current_rsi <= rsi_oversold:
                if state.position_side == 0:
                    # First long entry (10% of equity)
                    position_value = state.total_equity * 0.10
                    position_size = position_value / current_price
                    entry_fee = position_value * trading_fee_rate

                    if state.cash >= position_value + entry_fee:
                        state.position_side = 1
                        state.pyramid_level = 1
                        state.add_to_position(current_price, position_size)
                        state.cash -= position_value + entry_fee
                        state.waiting_for_reversion = False
                        signal = 1

                elif state.pyramid_level < 3 and not state.waiting_for_reversion:
                    # Additional long entries
                    if state.pyramid_level == 1:
                        position_value = state.total_equity * 0.20
                    else:  # pyramid_level == 2
                        position_value = state.total_equity * 0.70

                    position_size = position_value / current_price
                    entry_fee = position_value * trading_fee_rate

                    if state.cash >= position_value + entry_fee:
                        state.pyramid_level += 1
                        state.add_to_position(current_price, position_size)
                        state.cash -= position_value + entry_fee
                        state.waiting_for_reversion = False
                        signal = 1

            elif current_rsi >= rsi_middle and state.position_side == 1:
                # RSI hit 50, now wait for reversion to oversold
                state.waiting_for_reversion = True

            elif current_rsi >= rsi_overbought and state.position_side == 1:
                # Exit all longs and enter short
                exit_value = state.position_size * current_price
                exit_fee = exit_value * trading_fee_rate
                realized_pnl = (
                    current_price - state.average_entry_price
                ) * state.position_size - exit_fee

                state.cash += exit_value - exit_fee
                state.reset_position()

                # Enter short position (10% of new equity)
                state.total_equity = state.cash
                position_value = state.total_equity * 0.10
                position_size = position_value / current_price
                entry_fee = position_value * trading_fee_rate

                if state.cash >= position_value + entry_fee:
                    state.position_side = -1
                    state.pyramid_level = 1
                    state.add_to_position(current_price, position_size)
                    state.cash -= position_value + entry_fee
                    state.waiting_for_reversion = False
                    signal = -1

        # Short side logic
        elif state.position_side == -1:  # Short position
            if current_rsi >= rsi_overbought:
                if state.pyramid_level < 3 and not state.waiting_for_reversion:
                    # Additional short entries
                    if state.pyramid_level == 1:
                        position_value = state.total_equity * 0.20
                    else:  # pyramid_level == 2
                        position_value = state.total_equity * 0.70

                    position_size = position_value / current_price
                    entry_fee = position_value * trading_fee_rate

                    if state.cash >= position_value + entry_fee:
                        state.pyramid_level += 1
                        state.add_to_position(current_price, position_size)
                        state.cash -= position_value + entry_fee
                        state.waiting_for_reversion = False
                        signal = -1

            elif current_rsi <= rsi_middle:
                # RSI hit 50, now wait for reversion to overbought
                state.waiting_for_reversion = True

            elif current_rsi <= rsi_oversold:
                # Exit all shorts and enter long
                exit_value = state.position_size * current_price
                exit_fee = exit_value * trading_fee_rate
                realized_pnl = (
                    state.average_entry_price - current_price
                ) * state.position_size - exit_fee

                state.cash += exit_value - exit_fee
                state.reset_position()

                # Enter long position (10% of new equity)
                state.total_equity = state.cash
                position_value = state.total_equity * 0.10
                position_size = position_value / current_price
                entry_fee = position_value * trading_fee_rate

                if state.cash >= position_value + entry_fee:
                    state.position_side = 1
                    state.pyramid_level = 1
                    state.add_to_position(current_price, position_size)
                    state.cash -= position_value + entry_fee
                    state.waiting_for_reversion = False
                    signal = 1

        # Update DataFrame with current state
        df.loc[df.index[i], "signal"] = signal
        df.loc[df.index[i], "position_side"] = state.position_side
        df.loc[df.index[i], "position_size"] = state.position_size
        df.loc[df.index[i], "average_entry_price"] = state.average_entry_price
        df.loc[df.index[i], "cash"] = state.cash
        df.loc[df.index[i], "total_equity"] = state.total_equity
        df.loc[df.index[i], "pyramid_level"] = state.pyramid_level
        df.loc[df.index[i], "equity_curve"] = state.total_equity

    return df
