# RSI-Based Martingale Strategy

This implementation provides a sophisticated RSI-based martingale trading strategy with pyramiding capabilities, designed for cryptocurrency backtesting on 1-hour timeframes.

## Strategy Overview

The RSI Martingale strategy implements a systematic approach to trading based on RSI (Relative Strength Index) extremes with position pyramiding:

### Core Logic

**Long Side:**
- **Entry**: When RSI ≤ 30, open long position with 10% of wallet equity
- **Pyramiding**: If RSI rises to 50 and falls back to ≤ 30 (without hitting ≥ 70), add 20% of wallet
- **Final Pyramid**: If the pattern repeats, add remaining 70% of wallet
- **Exit**: When RSI ≥ 70, close all longs and flip to short

**Short Side:**
- **Entry**: When RSI ≥ 70, open short position with 10% of wallet equity  
- **Pyramiding**: If RSI falls to 50 and rises back to ≥ 70 (without hitting ≤ 30), add 20% of wallet
- **Final Pyramid**: If the pattern repeats, add remaining 70% of wallet
- **Exit**: When RSI ≤ 30, close all shorts and flip to long

### Key Features

- **Position Sizing**: 10% → 20% → 70% pyramiding structure
- **Mark-to-Market**: Real-time equity tracking at every bar
- **Trading Fees**: 0.05% on both entry and exit transactions
- **No Leverage**: Position sizing based on available cash equity
- **State Tracking**: Comprehensive monitoring of position, cash, and pyramid levels

## Files Structure

```
quanttrade/strategies/rsi_martingale.py    # Core strategy implementation
rsi_martingale_backtest.py                # Standalone backtesting script
example_rsi_martingale.py                 # Example usage with sample data
RSI_MARTINGALE_README.md                  # This documentation
```

## Usage

### 1. Standalone Backtesting Script

The main backtesting script supports both CSV files and live CCXT exchange data:

```bash
# Using CSV file
python rsi_martingale_backtest.py --csv data/BTCUSDT_1h.csv

# Using CCXT exchange
python rsi_martingale_backtest.py --exchange binance --symbol BTC/USDT --timeframe 1h --days 365

# With custom parameters
python rsi_martingale_backtest.py --csv data/ETHUSDT_1h.csv \
    --initial-capital 50000 \
    --trading-fee 0.001 \
    --rsi-period 21 \
    --rsi-oversold 25 \
    --rsi-overbought 75 \
    --save-csv \
    --output-dir results/eth_backtest
```

### 2. Integration with Existing Framework

```python
from quanttrade.strategies.rsi_martingale import rsi_martingale_strategy
from quanttrade.data.loader import load_crypto_data

# Load data
df = load_crypto_data('data/BTCUSDT_1h.csv')

# Run strategy
result = rsi_martingale_strategy(df, 
    rsi_period=14,
    rsi_oversold=30,
    rsi_overbought=70,
    initial_capital=10000.0,
    trading_fee_rate=0.0005
)

# Access results
equity_curve = result['equity_curve']
final_equity = equity_curve.iloc[-1]
```

### 3. Example with Sample Data

```bash
python example_rsi_martingale.py
```

## Strategy Parameters

| Parameter | Default | Description |
|-----------|---------|-------------|
| `rsi_period` | 14 | RSI calculation period |
| `rsi_oversold` | 30 | RSI oversold threshold for long entries |
| `rsi_overbought` | 70 | RSI overbought threshold for short entries |
| `rsi_middle` | 50 | RSI middle level for reversion detection |
| `initial_capital` | 10000.0 | Starting capital amount |
| `trading_fee_rate` | 0.0005 | Trading fee rate (0.05%) |

## Output Columns

The strategy adds the following columns to the input DataFrame:

| Column | Description |
|--------|-------------|
| `RSI` | 14-period RSI values |
| `signal` | Trading signals (1=buy, -1=sell, 0=hold) |
| `position_side` | Current position (1=long, -1=short, 0=flat) |
| `position_size` | Total position size in base currency |
| `average_entry_price` | Volume-weighted average entry price |
| `cash` | Available cash balance |
| `total_equity` | Total equity (cash + position value) |
| `pyramid_level` | Current pyramid level (0-3) |
| `unrealized_pnl` | Mark-to-market unrealized P&L |
| `equity_curve` | Real-time equity curve |

## Performance Metrics

The backtesting script calculates comprehensive performance metrics:

- **Total Return**: Overall percentage return
- **Annualized Return**: Annualized percentage return
- **Sharpe Ratio**: Risk-adjusted return metric
- **Maximum Drawdown**: Largest peak-to-trough decline
- **Win Rate**: Percentage of profitable periods
- **Profit Factor**: Ratio of total wins to total losses
- **Average Win/Loss**: Average percentage gains and losses

## Visualization

The script generates comprehensive plots including:

1. **Price and RSI Chart**: Shows price action with RSI overlay and threshold levels
2. **Position Tracking**: Displays position side and pyramid levels over time
3. **Equity Curve**: Shows portfolio value evolution
4. **Cash and Position Size**: Tracks available cash and position sizing

## Risk Considerations

⚠️ **Important Risk Warnings:**

1. **Martingale Risk**: This strategy can experience significant drawdowns during trending markets
2. **Capital Requirements**: Pyramiding requires substantial capital reserves
3. **Market Conditions**: Performance varies significantly across different market regimes
4. **Backtesting Limitations**: Historical performance does not guarantee future results

## Data Requirements

- **Timeframe**: Designed for 1-hour candles
- **Format**: OHLCV data with timestamp
- **Minimum Period**: At least 100 periods for RSI calculation
- **Quality**: Clean data without gaps or anomalies

## Dependencies

```python
pandas>=1.3.0
numpy>=1.20.0
matplotlib>=3.3.0
ccxt>=2.0.0  # Optional, for live data fetching
```

## Example Results

When run on sample data, the strategy typically shows:
- Multiple pyramid entries during RSI extremes
- Position flipping between long and short based on RSI levels
- Mark-to-market equity tracking with fee impact
- Detailed trade-by-trade analysis

## Customization

The strategy can be easily customized by modifying:
- RSI thresholds for different market conditions
- Pyramiding percentages (currently 10%/20%/70%)
- Fee structures for different exchanges
- Additional filters or conditions

## Support

For questions or issues with the RSI Martingale strategy implementation, please refer to the main QuantTrade documentation or create an issue in the repository.
