import ccxt, pandas as pd, numpy as np
from datetime import datetime, timedelta

def fetch_pair(exchange, symbol, tf="1h", lookback_days=180):
    since = int((datetime.utcnow()-timedelta(days=lookback_days)).timestamp()*1000)
    ohlcv = exchange.fetch_ohlcv(symbol, timeframe=tf, since=since, limit=None)
    df = pd.DataFrame(ohlcv, columns=["ts","o","h","l","c","v"])
    df["ts"] = pd.to_datetime(df["ts"], unit="ms")
    return df.set_index("ts")["c"]

ex = ccxt.binance()
btc = fetch_pair(ex, "BTC/USDT")
eth = fetch_pair(ex, "ETH/USDT")
prices = pd.concat({"btc": btc, "eth": eth}, axis=1).dropna()


btc = np.log(fetch_pair(ex, "BTC/USDT", tf="1h", lookback_days=365))
eth = np.log(fetch_pair(ex, "ETH/USDT", tf="1h", lookback_days=365))

# align, fill gaps
prices = (pd.concat({"btc": btc, "eth": eth}, axis=1)
            .asfreq("1h")
            .ffill()
            .dropna())

score, pval, _ = coint(prices["btc"], prices["eth"])
print(f"Engle-Granger p-value: {pval:.4f}")


import statsmodels.api as sm
window = 24*60   # 60 days of hourly bars
pvals = prices["btc"].rolling(window).apply(
    lambda _: coint(prices["btc"].loc[_].values,
                    prices["eth"].loc[_].values)[1],
    raw=False)

ok = pvals < 0.05           # periods where the pair is coint

from statsmodels.tsa.vector_ar.vecm import coint_johansen
res = coint_johansen(prices[["btc","eth"]], det_order=0, k_ar_diff=1)
print("trace stat:", res.lr1[0],  "crit @5%:", res.cvt[0,1])


import numpy as np, pandas as pd, statsmodels.api as sm

def one_slice(df, train_end, test_end,
              entry=2, exit=0.5, fee=0.00035):
    """
    df        : log-price DataFrame with columns ['btc','eth']
    train_end : last date of in-sample window (Timestamp)
    test_end  : last date of out-of-sample slice  (Timestamp)
    """
    train = df[:train_end]
    test  = df[train_end:test_end]

    # ---- 1. hedge ratio from in-sample OLS ----
    X = sm.add_constant(train['eth'])
    hedge = sm.OLS(train['btc'], X).fit().params['eth']

    # ---- 2. build spread & z on *training* mean/σ ----
    spread_train = train['btc'] - hedge*train['eth']
    μ, σ = spread_train.mean(), spread_train.std()

    spread_test = test['btc'] - hedge*test['eth']
    z = (spread_test - μ) / σ

    # ---- 3. signals ----
    sig  = pd.Series(0, index=z.index, dtype=int)
    sig[z <= -entry] = +1          # long spread
    sig[z >=  entry] = -1          # short spread
    sig[abs(z) < exit]  = 0        # flat trigger
    pos = sig.replace(0, np.nan).ffill().fillna(0)  # hold until reverse

    # ---- 4. PnL (shift pos to avoid look-ahead) ----
    ret_b = df['btc'].pct_change().loc[test.index]
    ret_e = df['eth'].pct_change().loc[test.index]
    spread_ret = ret_b - hedge*ret_e
    gross = pos.shift() * spread_ret       # hourly %  
    fees  = abs(pos.diff()) * fee          # enter/exit cost
    net   = gross - fees
    return net, pos


lookback = 60*24        # 60 days * 24 h
step     = 30*24        # rebalance every 30 days

equity   = []
cursor   = prices.index[lookback]

while cursor + pd.Timedelta(hours=step) < prices.index[-1]:
    train_end = cursor
    test_end  = cursor + pd.Timedelta(hours=step)
    pnl, _    = one_slice(prices, train_end, test_end)
    equity.append(pnl)
    cursor = test_end

equity = pd.concat(equity).sort_index().fillna(0)
curve  = (1 + equity).cumprod()


# ─── 0.  Imports ────────────────────────────────────────────────────────────
import ccxt, pandas as pd, numpy as np, math, time
import statsmodels.api as sm
from statsmodels.tsa.stattools import coint
from datetime import datetime, timedelta, timezone

# ─── 1.  Data download helper (loops past the 500/1500-bar cap) ─────────────
def fetch_pair_full(exchange, symbol, tf="1h", lookback_days=365, limit_per_call=1500):
    since = int((datetime.now(timezone.utc) - timedelta(days=lookback_days)).timestamp()*1000)
    now   = exchange.milliseconds()
    all_ohlcv = []

    while since < now:
        ohlcv = exchange.fetch_ohlcv(symbol, timeframe=tf, since=since, limit=limit_per_call)
        if not ohlcv:                                   # reached the end
            break
        all_ohlcv.extend(ohlcv)
        since = ohlcv[-1][0] + 1                        # next ms
        time.sleep(exchange.rateLimit / 1000)           # be polite

    df = (pd.DataFrame(all_ohlcv, columns=["ts","o","h","l","c","v"])
            .drop_duplicates(subset="ts")
            .set_index("ts"))
    df.index = pd.to_datetime(df.index, unit="ms", utc=True)
    return np.log(df["c"]).asfreq(tf).ffill()           # log-price Series

# ─── 2.  Download 1-year hourly closes for BTC & ETH ────────────────────────
ex   = ccxt.binance()
btc  = fetch_pair_full(ex, "BTC/USDT", tf="1h", lookback_days=365)
eth  = fetch_pair_full(ex, "ETH/USDT", tf="1h", lookback_days=365)
prices = pd.concat({"btc": btc, "eth": eth}, axis=1).dropna()
print("Fetched rows:", len(prices))                     # ~8 700

# ─── 3.  Cointegration sanity check ─────────────────────────────────────────
score, pval, _ = coint(prices["btc"], prices["eth"])
print(f"Engle-Granger p-value: {pval:.4f}")
if pval > 0.10:
    raise ValueError("Pair not cointegrated enough to trade. "
                     "Extend look-back or choose another pair.")

# ─── 4.  One-slice back-test function (re-fit hedge, trade, PnL) ───────────
def one_slice(df, train_end, test_end, entry=2, exit=0.5, fee=0.00035):
    train, test = df[:train_end], df[train_end:test_end]

    hedge = sm.OLS(train["btc"], sm.add_constant(train["eth"])).fit().params["eth"]

    spread_train = train["btc"] - hedge*train["eth"]
    mu, sigma    = spread_train.mean(), spread_train.std()

    spread_test  = test["btc"] - hedge*test["eth"]
    z            = (spread_test - mu) / sigma

    sig = pd.Series(0, index=z.index, dtype=int)
    sig[z <= -entry], sig[z >= entry] = +1, -1
    sig[abs(z) < exit] = 0
    pos = sig.replace(0, np.nan).ffill().fillna(0)

    ret_b, ret_e = df["btc"].pct_change().loc[test.index], df["eth"].pct_change().loc[test.index]
    spread_ret   = ret_b - hedge*ret_e

    gross = pos.shift() * spread_ret
    costs = abs(pos.diff()) * fee
    return (gross - costs).fillna(0)

# ─── 5.  Walk-forward loop (60-day look-back, 30-day step) ─────────────────
lookback = 60*24             # 1 440 bars = 60 d
step     = 30*24             # 720 bars = 30 d
if len(prices) <= lookback:
    raise ValueError("Not enough data for the chosen look-back window.")

cursor = prices.index[lookback]
equity = []

while cursor + pd.Timedelta(hours=step) < prices.index[-1]:
    train_end = cursor
    test_end  = cursor + pd.Timedelta(hours=step)
    equity.append(one_slice(prices, train_end, test_end))
    cursor = test_end

equity = pd.concat(equity).sort_index()
equity_curve = (1 + equity).cumprod()

# ─── 6.  Performance metrics ───────────────────────────────────────────────
ann_factor = math.sqrt(24*365)
sharpe = ann_factor * equity.mean() / equity.std()
duration_years = len(equity_curve) / 24 / 365
cagr   = equity_curve.iloc[-1]**(1/duration_years) - 1
maxdd  = (equity_curve / equity_curve.cummax() - 1).min()

print(f"\n── Results ─────────────────────────")
print(f"Sample length : {duration_years:.2f} yrs")
print(f"CAGR          : {cagr:.2%}")
print(f"Sharpe (ann.) : {sharpe:.2f}")
print(f"Max drawdown  : {maxdd:.2%}")

# equity_curve now holds the full curve; e.g. equity_curve.plot()
