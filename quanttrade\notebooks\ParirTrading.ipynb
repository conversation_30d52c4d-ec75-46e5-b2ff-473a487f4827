{"cells": [{"cell_type": "code", "execution_count": 6, "id": "9d9260dc", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_22204\\1906174366.py:5: DeprecationWarning: datetime.datetime.utcnow() is deprecated and scheduled for removal in a future version. Use timezone-aware objects to represent datetimes in UTC: datetime.datetime.now(datetime.UTC).\n", "  since = int((datetime.utcnow()-timedelta(days=lookback_days)).timestamp()*1000)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_22204\\1906174366.py:5: DeprecationWarning: datetime.datetime.utcnow() is deprecated and scheduled for removal in a future version. Use timezone-aware objects to represent datetimes in UTC: datetime.datetime.now(datetime.UTC).\n", "  since = int((datetime.utcnow()-timedelta(days=lookback_days)).timestamp()*1000)\n"]}], "source": ["import ccxt, pandas as pd, numpy as np\n", "from datetime import datetime, timedelta\n", "\n", "def fetch_pair(exchange, symbol, tf=\"1h\", lookback_days=180):\n", "    since = int((datetime.utcnow()-timedelta(days=lookback_days)).timestamp()*1000)\n", "    ohlcv = exchange.fetch_ohlcv(symbol, timeframe=tf, since=since, limit=None)\n", "    df = pd.DataFrame(ohlcv, columns=[\"ts\",\"o\",\"h\",\"l\",\"c\",\"v\"])\n", "    df[\"ts\"] = pd.to_datetime(df[\"ts\"], unit=\"ms\")\n", "    return df.set_index(\"ts\")[\"c\"]\n", "\n", "ex = ccxt.binance()\n", "btc = fetch_pair(ex, \"BTC/USDT\")\n", "eth = fetch_pair(ex, \"ETH/USDT\")\n", "prices = pd.concat({\"btc\": btc, \"eth\": eth}, axis=1).dropna()\n"]}, {"cell_type": "code", "execution_count": 7, "id": "074f1a95", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_22204\\1906174366.py:5: DeprecationWarning: datetime.datetime.utcnow() is deprecated and scheduled for removal in a future version. Use timezone-aware objects to represent datetimes in UTC: datetime.datetime.now(datetime.UTC).\n", "  since = int((datetime.utcnow()-timedelta(days=lookback_days)).timestamp()*1000)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_22204\\1906174366.py:5: DeprecationWarning: datetime.datetime.utcnow() is deprecated and scheduled for removal in a future version. Use timezone-aware objects to represent datetimes in UTC: datetime.datetime.now(datetime.UTC).\n", "  since = int((datetime.utcnow()-timedelta(days=lookback_days)).timestamp()*1000)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Engle-Granger p-value: 0.8273\n", "trace stat: 8.95414858442774 crit @5%: 15.4943\n"]}], "source": ["btc = np.log(fetch_pair(ex, \"BTC/USDT\", tf=\"1h\", lookback_days=365))\n", "eth = np.log(fetch_pair(ex, \"ETH/USDT\", tf=\"1h\", lookback_days=365))\n", "\n", "# align, fill gaps\n", "prices = (pd.concat({\"btc\": btc, \"eth\": eth}, axis=1)\n", "            .asfreq(\"1h\")\n", "            .ffill()\n", "            .dropna())\n", "\n", "score, pval, _ = coint(prices[\"btc\"], prices[\"eth\"])\n", "print(f\"Engle-Granger p-value: {pval:.4f}\")\n", "\n", "\n", "import statsmodels.api as sm\n", "window = 24*60   # 60 days of hourly bars\n", "pvals = prices[\"btc\"].rolling(window).apply(\n", "    lambda _: coint(prices[\"btc\"].loc[_].values,\n", "                    prices[\"eth\"].loc[_].values)[1],\n", "    raw=False)\n", "\n", "ok = pvals < 0.05           # periods where the pair is coint\n", "\n", "from statsmodels.tsa.vector_ar.vecm import coint_johansen\n", "res = coint_johansen(prices[[\"btc\",\"eth\"]], det_order=0, k_ar_diff=1)\n", "print(\"trace stat:\", res.lr1[0],  \"crit @5%:\", res.cvt[0,1])\n"]}, {"cell_type": "code", "execution_count": 8, "id": "1dd07a59", "metadata": {}, "outputs": [], "source": ["import numpy as np, pandas as pd, statsmodels.api as sm\n", "\n", "def one_slice(df, train_end, test_end,\n", "              entry=2, exit=0.5, fee=0.00035):\n", "    \"\"\"\n", "    df        : log-price DataFrame with columns ['btc','eth']\n", "    train_end : last date of in-sample window (Timestamp)\n", "    test_end  : last date of out-of-sample slice  (Timestamp)\n", "    \"\"\"\n", "    train = df[:train_end]\n", "    test  = df[train_end:test_end]\n", "\n", "    # ---- 1. hedge ratio from in-sample OLS ----\n", "    X = sm.add_constant(train['eth'])\n", "    hedge = sm.OLS(train['btc'], X).fit().params['eth']\n", "\n", "    # ---- 2. build spread & z on *training* mean/σ ----\n", "    spread_train = train['btc'] - hedge*train['eth']\n", "    μ, σ = spread_train.mean(), spread_train.std()\n", "\n", "    spread_test = test['btc'] - hedge*test['eth']\n", "    z = (spread_test - μ) / σ\n", "\n", "    # ---- 3. signals ----\n", "    sig  = pd.Series(0, index=z.index, dtype=int)\n", "    sig[z <= -entry] = +1          # long spread\n", "    sig[z >=  entry] = -1          # short spread\n", "    sig[abs(z) < exit]  = 0        # flat trigger\n", "    pos = sig.replace(0, np.nan).ffill().fillna(0)  # hold until reverse\n", "\n", "    # ---- 4. PnL (shift pos to avoid look-ahead) ----\n", "    ret_b = df['btc'].pct_change().loc[test.index]\n", "    ret_e = df['eth'].pct_change().loc[test.index]\n", "    spread_ret = ret_b - hedge*ret_e\n", "    gross = pos.shift() * spread_ret       # hourly %  \n", "    fees  = abs(pos.diff()) * fee          # enter/exit cost\n", "    net   = gross - fees\n", "    return net, pos\n"]}, {"cell_type": "code", "execution_count": 9, "id": "ab8e16cf", "metadata": {}, "outputs": [{"ename": "IndexError", "evalue": "index 1440 is out of bounds for axis 0 with size 500", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mIndexError\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[9], line 5\u001b[0m\n\u001b[0;32m      2\u001b[0m step     \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m30\u001b[39m\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m24\u001b[39m        \u001b[38;5;66;03m# rebalance every 30 days\u001b[39;00m\n\u001b[0;32m      4\u001b[0m equity   \u001b[38;5;241m=\u001b[39m []\n\u001b[1;32m----> 5\u001b[0m cursor   \u001b[38;5;241m=\u001b[39m prices\u001b[38;5;241m.\u001b[39mindex[lookback]\n\u001b[0;32m      7\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m cursor \u001b[38;5;241m+\u001b[39m pd\u001b[38;5;241m.\u001b[39mT<PERSON><PERSON><PERSON>(hours\u001b[38;5;241m=\u001b[39mstep) \u001b[38;5;241m<\u001b[39m prices\u001b[38;5;241m.\u001b[39mindex[\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m]:\n\u001b[0;32m      8\u001b[0m     train_end \u001b[38;5;241m=\u001b[39m cursor\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pandas\\core\\indexes\\base.py:5389\u001b[0m, in \u001b[0;36mIndex.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   5386\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_integer(key) \u001b[38;5;129;01mor\u001b[39;00m is_float(key):\n\u001b[0;32m   5387\u001b[0m     \u001b[38;5;66;03m# GH#44051 exclude bool, which would return a 2d ndarray\u001b[39;00m\n\u001b[0;32m   5388\u001b[0m     key \u001b[38;5;241m=\u001b[39m com\u001b[38;5;241m.\u001b[39mcast_scalar_indexer(key)\n\u001b[1;32m-> 5389\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m getitem(key)\n\u001b[0;32m   5391\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(key, \u001b[38;5;28mslice\u001b[39m):\n\u001b[0;32m   5392\u001b[0m     \u001b[38;5;66;03m# This case is separated from the conditional above to avoid\u001b[39;00m\n\u001b[0;32m   5393\u001b[0m     \u001b[38;5;66;03m# pessimization com.is_bool_indexer and ndim checks.\u001b[39;00m\n\u001b[0;32m   5394\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_slice(key)\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py:381\u001b[0m, in \u001b[0;36mDatetimeLikeArrayMixin.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m    374\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m    375\u001b[0m \u001b[38;5;124;03mThis getitem defers to the underlying array, which by-definition can\u001b[39;00m\n\u001b[0;32m    376\u001b[0m \u001b[38;5;124;03monly handle list-likes, slices, and integer scalars\u001b[39;00m\n\u001b[0;32m    377\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m    378\u001b[0m \u001b[38;5;66;03m# Use cast as we know we will get back a DatetimeLikeArray or DTScalar,\u001b[39;00m\n\u001b[0;32m    379\u001b[0m \u001b[38;5;66;03m# but skip evaluating the Union at runtime for performance\u001b[39;00m\n\u001b[0;32m    380\u001b[0m \u001b[38;5;66;03m# (see https://github.com/pandas-dev/pandas/pull/44624)\u001b[39;00m\n\u001b[1;32m--> 381\u001b[0m result \u001b[38;5;241m=\u001b[39m cast(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mUnion[Self, DTScalarOrNaT]\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28msuper\u001b[39m()\u001b[38;5;241m.\u001b[39m\u001b[38;5;21m__getitem__\u001b[39m(key))\n\u001b[0;32m    382\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m lib\u001b[38;5;241m.\u001b[39mis_scalar(result):\n\u001b[0;32m    383\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m result\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py:284\u001b[0m, in \u001b[0;36mNDArrayBackedExtensionArray.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m    278\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__getitem__\u001b[39m(\n\u001b[0;32m    279\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[0;32m    280\u001b[0m     key: PositionalIndexer2D,\n\u001b[0;32m    281\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Self \u001b[38;5;241m|\u001b[39m Any:\n\u001b[0;32m    282\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m lib\u001b[38;5;241m.\u001b[39mis_integer(key):\n\u001b[0;32m    283\u001b[0m         \u001b[38;5;66;03m# fast-path\u001b[39;00m\n\u001b[1;32m--> 284\u001b[0m         result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_ndarray[key]\n\u001b[0;32m    285\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mndim \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m    286\u001b[0m             \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_box_func(result)\n", "\u001b[1;31mIndexError\u001b[0m: index 1440 is out of bounds for axis 0 with size 500"]}], "source": ["lookback = 60*24        # 60 days * 24 h\n", "step     = 30*24        # rebalance every 30 days\n", "\n", "equity   = []\n", "cursor   = prices.index[lookback]\n", "\n", "while cursor + pd.Timedelta(hours=step) < prices.index[-1]:\n", "    train_end = cursor\n", "    test_end  = cursor + pd.Timedelta(hours=step)\n", "    pnl, _    = one_slice(prices, train_end, test_end)\n", "    equity.append(pnl)\n", "    cursor = test_end\n", "\n", "equity = pd.concat(equity).sort_index().fillna(0)\n", "curve  = (1 + equity).cumprod()\n"]}, {"cell_type": "code", "execution_count": 10, "id": "7bcd3f56", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fetched rows: 8760\n", "Engle-Granger p-value: 0.9530\n"]}, {"ename": "ValueError", "evalue": "Pair not cointegrated enough to trade. Extend look-back or choose another pair.", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[10], line 38\u001b[0m\n\u001b[0;32m     36\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mEngle-Granger p-value: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpval\u001b[38;5;132;01m:\u001b[39;00m\u001b[38;5;124m.4f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m     37\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m pval \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m0.10\u001b[39m:\n\u001b[1;32m---> 38\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mPair not cointegrated enough to trade. \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m     39\u001b[0m                      \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mExtend look-back or choose another pair.\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m     41\u001b[0m \u001b[38;5;66;03m# ─── 4.  One-slice back-test function (re-fit hedge, trade, PnL) ───────────\u001b[39;00m\n\u001b[0;32m     42\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mone_slice\u001b[39m(df, train_end, test_end, entry\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m2\u001b[39m, exit\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0.5\u001b[39m, fee\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0.00035\u001b[39m):\n", "\u001b[1;31mValueError\u001b[0m: Pair not cointegrated enough to trade. Extend look-back or choose another pair."]}], "source": ["# ─── 0.  <PERSON><PERSON>rts ────────────────────────────────────────────────────────────\n", "import ccxt, pandas as pd, numpy as np, math, time\n", "import statsmodels.api as sm\n", "from statsmodels.tsa.stattools import coint\n", "from datetime import datetime, timedelta, timezone\n", "\n", "# ─── 1.  Data download helper (loops past the 500/1500-bar cap) ─────────────\n", "def fetch_pair_full(exchange, symbol, tf=\"1h\", lookback_days=365, limit_per_call=1500):\n", "    since = int((datetime.now(timezone.utc) - timedelta(days=lookback_days)).timestamp()*1000)\n", "    now   = exchange.milliseconds()\n", "    all_ohlcv = []\n", "\n", "    while since < now:\n", "        ohlcv = exchange.fetch_ohlcv(symbol, timeframe=tf, since=since, limit=limit_per_call)\n", "        if not ohlcv:                                   # reached the end\n", "            break\n", "        all_ohlcv.extend(ohlcv)\n", "        since = ohlcv[-1][0] + 1                        # next ms\n", "        time.sleep(exchange.rateLimit / 1000)           # be polite\n", "\n", "    df = (pd.DataFrame(all_ohlcv, columns=[\"ts\",\"o\",\"h\",\"l\",\"c\",\"v\"])\n", "            .drop_duplicates(subset=\"ts\")\n", "            .set_index(\"ts\"))\n", "    df.index = pd.to_datetime(df.index, unit=\"ms\", utc=True)\n", "    return np.log(df[\"c\"]).asfreq(tf).ffill()           # log-price Series\n", "\n", "# ─── 2.  Download 1-year hourly closes for BTC & ETH ────────────────────────\n", "ex   = ccxt.binance()\n", "btc  = fetch_pair_full(ex, \"BTC/USDT\", tf=\"1h\", lookback_days=365)\n", "eth  = fetch_pair_full(ex, \"ETH/USDT\", tf=\"1h\", lookback_days=365)\n", "prices = pd.concat({\"btc\": btc, \"eth\": eth}, axis=1).dropna()\n", "print(\"Fetched rows:\", len(prices))                     # ~8 700\n", "\n", "# ─── 3.  Cointegration sanity check ─────────────────────────────────────────\n", "score, pval, _ = coint(prices[\"btc\"], prices[\"eth\"])\n", "print(f\"Engle-Granger p-value: {pval:.4f}\")\n", "if pval > 0.10:\n", "    raise ValueError(\"Pair not cointegrated enough to trade. \"\n", "                     \"Extend look-back or choose another pair.\")\n", "\n", "# ─── 4.  One-slice back-test function (re-fit hedge, trade, PnL) ───────────\n", "def one_slice(df, train_end, test_end, entry=2, exit=0.5, fee=0.00035):\n", "    train, test = df[:train_end], df[train_end:test_end]\n", "\n", "    hedge = sm.OLS(train[\"btc\"], sm.add_constant(train[\"eth\"])).fit().params[\"eth\"]\n", "\n", "    spread_train = train[\"btc\"] - hedge*train[\"eth\"]\n", "    mu, sigma    = spread_train.mean(), spread_train.std()\n", "\n", "    spread_test  = test[\"btc\"] - hedge*test[\"eth\"]\n", "    z            = (spread_test - mu) / sigma\n", "\n", "    sig = pd.Series(0, index=z.index, dtype=int)\n", "    sig[z <= -entry], sig[z >= entry] = +1, -1\n", "    sig[abs(z) < exit] = 0\n", "    pos = sig.replace(0, np.nan).ffill().fillna(0)\n", "\n", "    ret_b, ret_e = df[\"btc\"].pct_change().loc[test.index], df[\"eth\"].pct_change().loc[test.index]\n", "    spread_ret   = ret_b - hedge*ret_e\n", "\n", "    gross = pos.shift() * spread_ret\n", "    costs = abs(pos.diff()) * fee\n", "    return (gross - costs).fillna(0)\n", "\n", "# ─── 5.  Walk-forward loop (60-day look-back, 30-day step) ─────────────────\n", "lookback = 60*24             # 1 440 bars = 60 d\n", "step     = 30*24             # 720 bars = 30 d\n", "if len(prices) <= lookback:\n", "    raise ValueError(\"Not enough data for the chosen look-back window.\")\n", "\n", "cursor = prices.index[lookback]\n", "equity = []\n", "\n", "while cursor + pd.Timedelta(hours=step) < prices.index[-1]:\n", "    train_end = cursor\n", "    test_end  = cursor + pd.Timedelta(hours=step)\n", "    equity.append(one_slice(prices, train_end, test_end))\n", "    cursor = test_end\n", "\n", "equity = pd.concat(equity).sort_index()\n", "equity_curve = (1 + equity).cumprod()\n", "\n", "# ─── 6.  Performance metrics ───────────────────────────────────────────────\n", "ann_factor = math.sqrt(24*365)\n", "sharpe = ann_factor * equity.mean() / equity.std()\n", "duration_years = len(equity_curve) / 24 / 365\n", "cagr   = equity_curve.iloc[-1]**(1/duration_years) - 1\n", "maxdd  = (equity_curve / equity_curve.cummax() - 1).min()\n", "\n", "print(f\"\\n── Results ─────────────────────────\")\n", "print(f\"Sample length : {duration_years:.2f} yrs\")\n", "print(f\"CAGR          : {cagr:.2%}\")\n", "print(f\"Sharpe (ann.) : {sharpe:.2f}\")\n", "print(f\"Max drawdown  : {maxdd:.2%}\")\n", "\n", "# equity_curve now holds the full curve; e.g. equity_curve.plot()\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}