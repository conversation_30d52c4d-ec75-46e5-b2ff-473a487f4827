#!/usr/bin/env python3
"""
BTC Open Interest / Market Cap Ratio Analysis

This script fetches BTC open interest, market cap, and price data to create
a comprehensive comparison chart showing the OI/Market Cap ratio alongside price.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import requests
import time
from datetime import datetime, timedelta
import sys
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Add quanttrade to path
sys.path.append(str(Path(__file__).parent))

from quanttrade.data.loader import load_crypto_data, resample_ohlcv


def fetch_coingecko_market_data(days: int = 365) -> pd.DataFrame:
    """
    Fetch BTC market cap and price data from CoinGecko API.
    
    Args:
        days: Number of days to fetch (max 365 for free API)
        
    Returns:
        DataFrame with timestamp, price, and market cap
    """
    print("Fetching BTC market data from CoinGecko...")
    
    url = f"https://api.coingecko.com/api/v3/coins/bitcoin/market_chart"
    params = {
        'vs_currency': 'usd',
        'days': days,
        'interval': 'daily' if days > 90 else 'hourly'
    }
    
    try:
        response = requests.get(url, params=params, timeout=30)
        response.raise_for_status()
        data = response.json()
        
        # Extract price and market cap data
        prices = data['prices']
        market_caps = data['market_caps']
        
        # Convert to DataFrame
        df_price = pd.DataFrame(prices, columns=['timestamp', 'price'])
        df_mcap = pd.DataFrame(market_caps, columns=['timestamp', 'market_cap'])
        
        # Merge on timestamp
        df = pd.merge(df_price, df_mcap, on='timestamp')
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        
        print(f"Fetched {len(df)} records from CoinGecko")
        return df
        
    except Exception as e:
        print(f"Error fetching CoinGecko data: {e}")
        return None


def fetch_binance_open_interest() -> pd.DataFrame:
    """
    Fetch BTC open interest data from Binance Futures API.
    
    Returns:
        DataFrame with timestamp and open interest
    """
    print("Fetching BTC open interest from Binance...")
    
    try:
        # Get current open interest
        url = "https://fapi.binance.com/fapi/v1/openInterest"
        params = {'symbol': 'BTCUSDT'}
        
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        current_oi = response.json()
        
        # Get historical open interest (last 30 days)
        url_hist = "https://fapi.binance.com/futures/data/openInterestHist"
        params_hist = {
            'symbol': 'BTCUSDT',
            'period': '1d',
            'limit': 30
        }
        
        response_hist = requests.get(url_hist, params=params_hist, timeout=10)
        response_hist.raise_for_status()
        hist_data = response_hist.json()
        
        # Convert to DataFrame
        df = pd.DataFrame(hist_data)
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        df['open_interest'] = df['sumOpenInterest'].astype(float)
        df = df[['timestamp', 'open_interest']].copy()
        
        print(f"Fetched {len(df)} open interest records from Binance")
        return df
        
    except Exception as e:
        print(f"Error fetching Binance open interest: {e}")
        return None


def create_sample_oi_data(price_df: pd.DataFrame) -> pd.DataFrame:
    """
    Create sample open interest data for demonstration.
    
    Args:
        price_df: DataFrame with price data
        
    Returns:
        DataFrame with synthetic open interest data
    """
    print("Creating sample open interest data for demonstration...")
    
    # Create synthetic OI data that correlates with price movements
    np.random.seed(42)
    
    df = price_df.copy()
    
    # Base OI around 500,000 BTC with some correlation to price
    base_oi = 500000
    price_normalized = (df['Close'] - df['Close'].min()) / (df['Close'].max() - df['Close'].min())
    
    # OI tends to increase with price but with some lag and noise
    oi_trend = base_oi * (1 + 0.5 * price_normalized)
    oi_noise = np.random.normal(0, base_oi * 0.1, len(df))
    oi_lag = np.roll(oi_trend, 10)  # 10-period lag
    
    df['open_interest'] = oi_lag + oi_noise
    df['open_interest'] = df['open_interest'].clip(lower=100000)  # Minimum OI
    
    return df[['Timestamp', 'open_interest']].copy()


def calculate_oi_marketcap_ratio(price_df: pd.DataFrame, oi_df: pd.DataFrame, mcap_df: pd.DataFrame = None) -> pd.DataFrame:
    """
    Calculate the Open Interest / Market Cap ratio.
    
    Args:
        price_df: DataFrame with BTC price data
        oi_df: DataFrame with open interest data
        mcap_df: DataFrame with market cap data (optional)
        
    Returns:
        DataFrame with OI/Market Cap ratio
    """
    print("Calculating OI/Market Cap ratio...")
    
    # Merge price and OI data
    df = pd.merge(price_df, oi_df, on='Timestamp', how='inner')
    
    if mcap_df is not None:
        # Use provided market cap data
        df = pd.merge(df, mcap_df, on='Timestamp', how='inner')
    else:
        # Calculate market cap from price (assuming ~19.7M BTC in circulation)
        btc_supply = 19700000  # Approximate current BTC supply
        df['market_cap'] = df['Close'] * btc_supply
    
    # Calculate OI/Market Cap ratio
    # Convert OI from BTC to USD value
    df['oi_usd_value'] = df['open_interest'] * df['Close']
    df['oi_marketcap_ratio'] = df['oi_usd_value'] / df['market_cap']
    
    return df


def create_oi_marketcap_chart(df: pd.DataFrame, save_path: str = "btc_oi_marketcap_analysis.png"):
    """
    Create comprehensive chart comparing OI/Market Cap ratio with BTC price.
    
    Args:
        df: DataFrame with all data
        save_path: Path to save the chart
    """
    print("Creating OI/Market Cap analysis chart...")
    
    fig, axes = plt.subplots(3, 1, figsize=(16, 12))
    
    # Plot 1: BTC Price
    ax1 = axes[0]
    ax1.plot(df['Timestamp'], df['Close'], color='orange', linewidth=2, label='BTC Price')
    ax1.set_ylabel('BTC Price ($)', fontsize=12, color='orange')
    ax1.set_title('BTC Price vs Open Interest/Market Cap Ratio Analysis', fontsize=16, fontweight='bold')
    ax1.tick_params(axis='y', labelcolor='orange')
    ax1.grid(True, alpha=0.3)
    ax1.legend(loc='upper left')
    
    # Format y-axis for price
    ax1.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))
    
    # Plot 2: Open Interest and Market Cap
    ax2 = axes[1]
    
    # Plot OI in BTC
    ax2_oi = ax2
    ax2_oi.plot(df['Timestamp'], df['open_interest'], color='blue', linewidth=2, label='Open Interest (BTC)')
    ax2_oi.set_ylabel('Open Interest (BTC)', fontsize=12, color='blue')
    ax2_oi.tick_params(axis='y', labelcolor='blue')
    
    # Plot Market Cap on secondary axis
    ax2_mcap = ax2.twinx()
    ax2_mcap.plot(df['Timestamp'], df['market_cap'] / 1e9, color='green', linewidth=2, label='Market Cap ($B)')
    ax2_mcap.set_ylabel('Market Cap ($ Billions)', fontsize=12, color='green')
    ax2_mcap.tick_params(axis='y', labelcolor='green')
    
    ax2.set_title('Open Interest vs Market Cap', fontsize=14, fontweight='bold')
    ax2.grid(True, alpha=0.3)
    
    # Add legends
    ax2_oi.legend(loc='upper left')
    ax2_mcap.legend(loc='upper right')
    
    # Plot 3: OI/Market Cap Ratio
    ax3 = axes[2]
    ax3.plot(df['Timestamp'], df['oi_marketcap_ratio'] * 100, color='red', linewidth=2, label='OI/Market Cap Ratio (%)')
    ax3.set_ylabel('OI/Market Cap Ratio (%)', fontsize=12, color='red')
    ax3.set_xlabel('Time', fontsize=12)
    ax3.set_title('Open Interest / Market Cap Ratio', fontsize=14, fontweight='bold')
    ax3.tick_params(axis='y', labelcolor='red')
    ax3.grid(True, alpha=0.3)
    ax3.legend()
    
    # Add horizontal reference lines for ratio
    ratio_mean = df['oi_marketcap_ratio'].mean() * 100
    ax3.axhline(y=ratio_mean, color='red', linestyle='--', alpha=0.5, label=f'Mean: {ratio_mean:.2f}%')
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Chart saved to: {save_path}")
    plt.show()


def main():
    """Main function to run the OI/Market Cap analysis."""
    
    print("=" * 70)
    print("BTC Open Interest / Market Cap Ratio Analysis")
    print("=" * 70)
    
    # Load BTC price data from your existing dataset
    try:
        print("Loading BTC price data...")
        btc_df = load_crypto_data(
            "quanttrade/data/datasets/btcusd_1_min_data.csv",
            start_date="2023-01-01",
            end_date="2024-12-31"
        )
        
        # Resample to daily for this analysis
        btc_daily = resample_ohlcv(btc_df, timeframe="1d")
        print(f"Using {len(btc_daily)} daily BTC price records")
        
    except Exception as e:
        print(f"Error loading BTC data: {e}")
        print("Creating sample price data for demonstration...")
        
        # Create sample data
        dates = pd.date_range(start='2023-01-01', end='2024-12-31', freq='D')
        np.random.seed(42)
        prices = 30000 + np.cumsum(np.random.normal(0, 1000, len(dates)))
        prices = np.clip(prices, 15000, 100000)
        
        btc_daily = pd.DataFrame({
            'Timestamp': dates,
            'Close': prices,
            'Open': prices * (1 + np.random.normal(0, 0.01, len(dates))),
            'High': prices * (1 + np.abs(np.random.normal(0, 0.02, len(dates)))),
            'Low': prices * (1 - np.abs(np.random.normal(0, 0.02, len(dates)))),
            'Volume': np.random.uniform(20000, 50000, len(dates))
        })
    
    # Try to fetch real market cap data
    mcap_df = fetch_coingecko_market_data(days=365)
    
    # Try to fetch real open interest data
    oi_df = fetch_binance_open_interest()
    
    # If real OI data is not available, create sample data
    if oi_df is None or len(oi_df) < 10:
        print("Using sample open interest data...")
        oi_df = create_sample_oi_data(btc_daily)
    
    # Prepare market cap data
    if mcap_df is not None:
        mcap_df = mcap_df.rename(columns={'timestamp': 'Timestamp'})
        mcap_df = mcap_df[['Timestamp', 'market_cap']]
    
    # Calculate OI/Market Cap ratio
    analysis_df = calculate_oi_marketcap_ratio(btc_daily, oi_df, mcap_df)
    
    # Print summary statistics
    print("\n" + "=" * 70)
    print("ANALYSIS SUMMARY")
    print("=" * 70)
    
    print(f"Data Period: {analysis_df['Timestamp'].min().date()} to {analysis_df['Timestamp'].max().date()}")
    print(f"Total Records: {len(analysis_df)}")
    
    print(f"\nBTC Price Statistics:")
    print(f"  Min Price: ${analysis_df['Close'].min():,.2f}")
    print(f"  Max Price: ${analysis_df['Close'].max():,.2f}")
    print(f"  Mean Price: ${analysis_df['Close'].mean():,.2f}")
    
    print(f"\nOpen Interest Statistics:")
    print(f"  Min OI: {analysis_df['open_interest'].min():,.0f} BTC")
    print(f"  Max OI: {analysis_df['open_interest'].max():,.0f} BTC")
    print(f"  Mean OI: {analysis_df['open_interest'].mean():,.0f} BTC")
    
    print(f"\nOI/Market Cap Ratio Statistics:")
    print(f"  Min Ratio: {analysis_df['oi_marketcap_ratio'].min()*100:.3f}%")
    print(f"  Max Ratio: {analysis_df['oi_marketcap_ratio'].max()*100:.3f}%")
    print(f"  Mean Ratio: {analysis_df['oi_marketcap_ratio'].mean()*100:.3f}%")
    
    # Create the chart
    create_oi_marketcap_chart(analysis_df)
    
    # Save data to CSV
    output_path = "btc_oi_marketcap_data.csv"
    analysis_df.to_csv(output_path, index=False)
    print(f"\nData saved to: {output_path}")
    
    print("\n✅ Analysis completed successfully!")
    
    return analysis_df


if __name__ == "__main__":
    result_df = main()
