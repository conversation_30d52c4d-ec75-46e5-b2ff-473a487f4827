#!/usr/bin/env python3
"""
Simple BTC Open Interest / Market Cap Ratio Chart

Creates a focused chart comparing BTC price with OI/Market Cap ratio.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import sys
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Add quanttrade to path
sys.path.append(str(Path(__file__).parent))

from quanttrade.data.loader import load_crypto_data, resample_ohlcv


def create_sample_data():
    """Create sample BTC data with OI/Market Cap ratio for demonstration."""
    
    print("Creating sample BTC data with OI/Market Cap analysis...")
    
    # Create date range for last 2 years
    dates = pd.date_range(start='2023-01-01', end='2024-12-31', freq='D')
    n_days = len(dates)
    
    # Set random seed for reproducible results
    np.random.seed(42)
    
    # Generate realistic BTC price movement
    initial_price = 16500  # Starting price
    daily_returns = np.random.normal(0.001, 0.04, n_days)  # 0.1% daily drift, 4% volatility
    
    # Add some trending periods
    trend_periods = [
        (50, 150, 0.003),   # Bull run
        (200, 300, -0.002), # Bear period
        (400, 500, 0.004),  # Strong bull
        (600, 650, -0.003)  # Correction
    ]
    
    for start, end, trend in trend_periods:
        if end < n_days:
            daily_returns[start:end] += trend
    
    # Calculate cumulative price
    price_series = initial_price * np.exp(np.cumsum(daily_returns))
    
    # Generate Open Interest data (correlated with price but with lag)
    base_oi = 400000  # Base OI in BTC
    price_normalized = (price_series - price_series.min()) / (price_series.max() - price_series.min())
    
    # OI increases with price but with some lag and noise
    oi_multiplier = 1 + 0.8 * np.roll(price_normalized, 20)  # 20-day lag
    oi_noise = np.random.normal(1, 0.15, n_days)
    open_interest = base_oi * oi_multiplier * oi_noise
    open_interest = np.clip(open_interest, 200000, 1000000)  # Realistic bounds
    
    # Calculate Market Cap (assuming ~19.7M BTC supply)
    btc_supply = 19700000
    market_cap = price_series * btc_supply
    
    # Calculate OI/Market Cap ratio
    oi_usd_value = open_interest * price_series
    oi_marketcap_ratio = oi_usd_value / market_cap
    
    # Create DataFrame
    df = pd.DataFrame({
        'Date': dates,
        'BTC_Price': price_series,
        'Open_Interest_BTC': open_interest,
        'Market_Cap_USD': market_cap,
        'OI_USD_Value': oi_usd_value,
        'OI_MarketCap_Ratio': oi_marketcap_ratio
    })
    
    return df


def create_oi_marketcap_comparison_chart(df):
    """Create the main comparison chart."""
    
    print("Creating BTC Price vs OI/Market Cap Ratio chart...")
    
    # Set up the figure with subplots
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10), sharex=True)
    
    # Plot 1: BTC Price
    color1 = '#F7931A'  # Bitcoin orange
    ax1.plot(df['Date'], df['BTC_Price'], color=color1, linewidth=2.5, label='BTC Price')
    ax1.set_ylabel('BTC Price ($)', fontsize=12, fontweight='bold', color=color1)
    ax1.tick_params(axis='y', labelcolor=color1)
    ax1.grid(True, alpha=0.3)
    ax1.set_title('Bitcoin Price vs Open Interest/Market Cap Ratio', fontsize=16, fontweight='bold', pad=20)
    
    # Format price axis
    ax1.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))
    
    # Add price statistics
    price_min, price_max = df['BTC_Price'].min(), df['BTC_Price'].max()
    ax1.axhline(y=price_min, color=color1, linestyle='--', alpha=0.5, linewidth=1)
    ax1.axhline(y=price_max, color=color1, linestyle='--', alpha=0.5, linewidth=1)
    
    # Plot 2: OI/Market Cap Ratio
    color2 = '#8A2BE2'  # Blue violet
    ax2.plot(df['Date'], df['OI_MarketCap_Ratio'] * 100, color=color2, linewidth=2.5, label='OI/Market Cap Ratio')
    ax2.set_ylabel('OI/Market Cap Ratio (%)', fontsize=12, fontweight='bold', color=color2)
    ax2.set_xlabel('Date', fontsize=12, fontweight='bold')
    ax2.tick_params(axis='y', labelcolor=color2)
    ax2.grid(True, alpha=0.3)
    
    # Add ratio statistics
    ratio_mean = df['OI_MarketCap_Ratio'].mean() * 100
    ratio_std = df['OI_MarketCap_Ratio'].std() * 100
    
    ax2.axhline(y=ratio_mean, color=color2, linestyle='-', alpha=0.7, linewidth=1, 
                label=f'Mean: {ratio_mean:.2f}%')
    ax2.axhline(y=ratio_mean + ratio_std, color=color2, linestyle='--', alpha=0.5, linewidth=1,
                label=f'+1σ: {ratio_mean + ratio_std:.2f}%')
    ax2.axhline(y=ratio_mean - ratio_std, color=color2, linestyle='--', alpha=0.5, linewidth=1,
                label=f'-1σ: {ratio_mean - ratio_std:.2f}%')
    
    # Add legend for ratio plot
    ax2.legend(loc='upper right', fontsize=10)
    
    # Improve layout
    plt.tight_layout()
    
    # Save the chart
    plt.savefig('btc_oi_marketcap_comparison.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    print("Chart saved as: btc_oi_marketcap_comparison.png")
    
    # Show the plot
    plt.show()
    
    return fig


def create_correlation_analysis(df):
    """Create additional correlation analysis."""
    
    print("\nPerforming correlation analysis...")
    
    # Calculate correlations
    price_oi_corr = df['BTC_Price'].corr(df['OI_MarketCap_Ratio'])
    
    # Calculate rolling correlation (30-day window)
    rolling_corr = df['BTC_Price'].rolling(window=30).corr(df['OI_MarketCap_Ratio'])
    
    print(f"Overall Price vs OI/Market Cap Correlation: {price_oi_corr:.3f}")
    print(f"Average 30-day Rolling Correlation: {rolling_corr.mean():.3f}")
    
    # Create correlation chart
    fig, ax = plt.subplots(1, 1, figsize=(12, 6))
    
    ax.plot(df['Date'], rolling_corr, color='red', linewidth=2, label='30-Day Rolling Correlation')
    ax.axhline(y=0, color='black', linestyle='-', alpha=0.5, linewidth=1)
    ax.axhline(y=price_oi_corr, color='red', linestyle='--', alpha=0.7, linewidth=1,
               label=f'Overall Correlation: {price_oi_corr:.3f}')
    
    ax.set_ylabel('Correlation Coefficient', fontsize=12, fontweight='bold')
    ax.set_xlabel('Date', fontsize=12, fontweight='bold')
    ax.set_title('BTC Price vs OI/Market Cap Ratio - Rolling Correlation', fontsize=14, fontweight='bold')
    ax.grid(True, alpha=0.3)
    ax.legend()
    
    plt.tight_layout()
    plt.savefig('btc_oi_correlation_analysis.png', dpi=300, bbox_inches='tight')
    print("Correlation chart saved as: btc_oi_correlation_analysis.png")
    plt.show()


def print_summary_statistics(df):
    """Print comprehensive summary statistics."""
    
    print("\n" + "="*70)
    print("BTC OPEN INTEREST / MARKET CAP ANALYSIS SUMMARY")
    print("="*70)
    
    print(f"\n📊 DATA OVERVIEW")
    print(f"Period: {df['Date'].min().strftime('%Y-%m-%d')} to {df['Date'].max().strftime('%Y-%m-%d')}")
    print(f"Total Days: {len(df)}")
    
    print(f"\n💰 BTC PRICE STATISTICS")
    print(f"Starting Price: ${df['BTC_Price'].iloc[0]:,.2f}")
    print(f"Ending Price: ${df['BTC_Price'].iloc[-1]:,.2f}")
    print(f"Minimum Price: ${df['BTC_Price'].min():,.2f}")
    print(f"Maximum Price: ${df['BTC_Price'].max():,.2f}")
    print(f"Average Price: ${df['BTC_Price'].mean():,.2f}")
    print(f"Total Return: {((df['BTC_Price'].iloc[-1] / df['BTC_Price'].iloc[0]) - 1) * 100:.1f}%")
    
    print(f"\n🔄 OPEN INTEREST STATISTICS")
    print(f"Min OI: {df['Open_Interest_BTC'].min():,.0f} BTC")
    print(f"Max OI: {df['Open_Interest_BTC'].max():,.0f} BTC")
    print(f"Average OI: {df['Open_Interest_BTC'].mean():,.0f} BTC")
    
    print(f"\n📈 OI/MARKET CAP RATIO STATISTICS")
    ratio_pct = df['OI_MarketCap_Ratio'] * 100
    print(f"Minimum Ratio: {ratio_pct.min():.3f}%")
    print(f"Maximum Ratio: {ratio_pct.max():.3f}%")
    print(f"Average Ratio: {ratio_pct.mean():.3f}%")
    print(f"Standard Deviation: {ratio_pct.std():.3f}%")
    
    # Identify extreme periods
    high_ratio_threshold = ratio_pct.quantile(0.9)
    low_ratio_threshold = ratio_pct.quantile(0.1)
    
    print(f"\n🚨 EXTREME RATIO PERIODS")
    print(f"High Ratio Threshold (90th percentile): {high_ratio_threshold:.3f}%")
    print(f"Low Ratio Threshold (10th percentile): {low_ratio_threshold:.3f}%")
    
    high_ratio_days = (ratio_pct > high_ratio_threshold).sum()
    low_ratio_days = (ratio_pct < low_ratio_threshold).sum()
    
    print(f"Days with High Ratio: {high_ratio_days} ({high_ratio_days/len(df)*100:.1f}%)")
    print(f"Days with Low Ratio: {low_ratio_days} ({low_ratio_days/len(df)*100:.1f}%)")


def main():
    """Main function to run the analysis."""
    
    print("🚀 BTC Open Interest / Market Cap Ratio Analysis")
    print("="*70)
    
    # Try to load real BTC data first
    try:
        print("Attempting to load real BTC data...")
        btc_df = load_crypto_data(
            "quanttrade/data/datasets/btcusd_1_min_data.csv",
            start_date="2023-01-01",
            end_date="2024-12-31"
        )
        
        # Resample to daily
        btc_daily = resample_ohlcv(btc_df, timeframe="1d")
        print(f"✅ Loaded {len(btc_daily)} days of real BTC data")
        
        # For this demo, we'll still use sample OI data since we don't have real OI data
        print("📊 Creating sample Open Interest data for demonstration...")
        df = create_sample_data()
        
        # Replace sample prices with real prices (matching dates)
        if len(btc_daily) > 0:
            # Align dates and use real prices where available
            btc_daily['Date'] = btc_daily['Timestamp'].dt.date
            df['Date_only'] = df['Date'].dt.date
            
            # Merge real prices where dates match
            merged = df.merge(btc_daily[['Date', 'Close']], 
                            left_on='Date', right_on='Date', how='left')
            
            # Use real prices where available
            mask = ~merged['Close'].isna()
            if mask.sum() > 0:
                df.loc[mask, 'BTC_Price'] = merged.loc[mask, 'Close']
                print(f"✅ Used real prices for {mask.sum()} days")
        
    except Exception as e:
        print(f"⚠️ Could not load real BTC data: {e}")
        print("📊 Using sample data for demonstration...")
        df = create_sample_data()
    
    # Print summary statistics
    print_summary_statistics(df)
    
    # Create the main comparison chart
    create_oi_marketcap_comparison_chart(df)
    
    # Create correlation analysis
    create_correlation_analysis(df)
    
    # Save data to CSV
    output_file = 'btc_oi_marketcap_analysis_data.csv'
    df.to_csv(output_file, index=False)
    print(f"\n💾 Data saved to: {output_file}")
    
    print("\n✅ Analysis completed successfully!")
    print("\n📋 Files created:")
    print("  • btc_oi_marketcap_comparison.png - Main comparison chart")
    print("  • btc_oi_correlation_analysis.png - Correlation analysis")
    print(f"  • {output_file} - Raw data")
    
    return df


if __name__ == "__main__":
    result_df = main()
