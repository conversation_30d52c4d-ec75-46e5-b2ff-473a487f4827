#!/bin/bash
# ---------- base OS ----------
yum update -y && yum install -y docker jq awscli
systemctl enable --now docker

# ---------- ECR login (token valid 12h) ----------
aws ecr get-login-password --region ap-southeast-1 | \
  docker login --username AWS --password-stdin 650251720729.dkr.ecr.ap-southeast-1.amazonaws.com

# ---------- pull image ----------
docker pull 650251720729.dkr.ecr.ap-southeast-1.amazonaws.com/trading-bot:latest

# ---------- fetch secrets & run ----------
eval $(aws secretsmanager get-secret-value --secret-id trading-bot/binance \
       --query SecretString --output text | \
       jq -r 'to_entries|map("-e \(.key)=\(.value)")|.[]')

eval $(aws secretsmanager get-secret-value --secret-id trading-bot/telegram \
       --query SecretString --output text | \
       jq -r 'to_entries|map("-e \(.key)=\(.value)")|.[]')

docker run -d --name trading-bot --restart unless-stopped \
  650251720729.dkr.ecr.ap-southeast-1.amazonaws.com/trading-bot:latest
