{"cells": [{"cell_type": "code", "execution_count": 8, "id": "8d7977b2", "metadata": {}, "outputs": [], "source": ["# %% 1 ▸ Imports & basic configuration\n", "import os, time, numpy as np, pandas as pd, matplotlib.pyplot as plt\n", "from binance.client import Client\n", "\n", "# -- API keys (store in env vars for safety)\n", "API_KEY    = \"k4qiTrIPpKFxW0Zu5fCUQ3rs8GrAQFrUSO9tRN9UnnAvCs6ZoPj6gS50Mh8Ki5tg\"      # or paste\n", "API_SECRET = \"ERT4TPVRNW21ghwBV5z2St2GcFVihEN81pAjESaCGzIxKtG5WTm6DLSMq3wMrD8M\"\n", "\n", "# -- parameters you’ll probably tweak\n", "SYMBOL       = \"BTCUSDT\"                # USDT-M perpetual\n", "START_DATE   = \"2023-06-23 00:00\"       # UTC\n", "RAW_CSV      = \"btc_premium_1m.csv\"     # where raw klines live\n", "\n", "CAPITAL_USD  = 10000                   # notional per trade\n", "TAKER_FEE    = 0.0004                   # 0.04 %\n", "\n", "client = Client(API_KEY, API_SECRET)\n"]}, {"cell_type": "code", "execution_count": 9, "id": "4c468f03", "metadata": {}, "outputs": [], "source": ["# %% 2 ▸ Helper – fetch every 1-minute premium-index kline\n", "def fetch_premium_1m(symbol: str, since: str) -> pd.DataFrame:\n", "    start = int(pd.to_datetime(since,  utc=True).timestamp() * 1000)\n", "    end   = int(time.time() * 1000)\n", "    out   = []\n", "\n", "    while start < end:\n", "        kl = client.futures_premium_index_klines(\n", "            symbol=symbol, interval=\"1m\",\n", "            startTime=start,\n", "            endTime=min(start + 1000*60*1000, end),\n", "            limit=1000\n", "        )\n", "        if not kl:\n", "            break\n", "        out.extend(kl)\n", "        start = kl[-1][0] + 60_000       # jump one bar ahead\n", "        time.sleep(0.25)                 # stay far below rate limits\n", "\n", "    df = pd.<PERSON><PERSON><PERSON>e(out)[[0,1,2,3,4]]          # keep first 5 cols\n", "    df.columns = [\"open_time\",\"open\",\"high\",\"low\",\"close\"]\n", "    df[\"open_time\"] = pd.to_datetime(df[\"open_time\"], unit=\"ms\", utc=True)\n", "    df.set_index(\"open_time\", inplace=True)\n", "    return df.astype(float)\n"]}, {"cell_type": "code", "execution_count": 10, "id": "3674bd59", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CSV already on disk - skipping download\n"]}], "source": ["# %% 3 ▸ Fetch *once* and save raw data   (skip if CSV already exists)\n", "if not os.path.exists(RAW_CSV):\n", "    raw = fetch_premium_1m(SYMBOL, START_DATE)\n", "    raw.to_csv(RAW_CSV)\n", "    print(f\"Saved {len(raw):,} rows → {RAW_CSV}\")\n", "else:\n", "    print(\"CSV already on disk - skipping download\")\n"]}, {"cell_type": "code", "execution_count": 11, "id": "c9564fdf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Rows read: 1054533 |  range: 2023-06-23 00:00:00+00:00 → 2025-06-24 07:48:00+00:00\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "    </tr>\n", "    <tr>\n", "      <th>open_time</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-06-23 00:00:00+00:00</th>\n", "      <td>-0.000418</td>\n", "      <td>0.000041</td>\n", "      <td>-0.000418</td>\n", "      <td>-0.000133</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-23 00:01:00+00:00</th>\n", "      <td>-0.000186</td>\n", "      <td>-0.000011</td>\n", "      <td>-0.000186</td>\n", "      <td>-0.000043</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-23 00:02:00+00:00</th>\n", "      <td>-0.000035</td>\n", "      <td>-0.000031</td>\n", "      <td>-0.000354</td>\n", "      <td>-0.000048</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-23 00:03:00+00:00</th>\n", "      <td>-0.000050</td>\n", "      <td>-0.000023</td>\n", "      <td>-0.000333</td>\n", "      <td>-0.000162</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-23 00:04:00+00:00</th>\n", "      <td>-0.000149</td>\n", "      <td>-0.000133</td>\n", "      <td>-0.000252</td>\n", "      <td>-0.000252</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                               open      high       low     close\n", "open_time                                                        \n", "2023-06-23 00:00:00+00:00 -0.000418  0.000041 -0.000418 -0.000133\n", "2023-06-23 00:01:00+00:00 -0.000186 -0.000011 -0.000186 -0.000043\n", "2023-06-23 00:02:00+00:00 -0.000035 -0.000031 -0.000354 -0.000048\n", "2023-06-23 00:03:00+00:00 -0.000050 -0.000023 -0.000333 -0.000162\n", "2023-06-23 00:04:00+00:00 -0.000149 -0.000133 -0.000252 -0.000252"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# %% 4 ▸ Load the raw CSV each session\n", "prem = pd.read_csv(\n", "    RAW_CSV,\n", "    parse_dates=[\"open_time\"],\n", "    index_col=\"open_time\"\n", ")\n", "print(\"Rows read:\", len(prem),\n", "      \"|  range:\", prem.index[0], \"→\", prem.index[-1])\n", "prem.head()\n"]}, {"cell_type": "code", "execution_count": null, "id": "9242204a", "metadata": {}, "outputs": [], "source": ["# %% 4 Define the 4-threshold backtester\n", "def backtest_4t(df, ent_low, ent_high, exit_low, exit_high):\n", "    \"\"\"\n", "    ent_low:  trigger long  when LOW ≤ ent_low  (eg. −0.2%)\n", "    ent_high: trigger short when HIGH ≥ ent_high (eg. +0.2%)\n", "    exit_low: exit long when CLOSE ≥ exit_low  (eg. −0.05%)\n", "    exit_high:exit short when CLOSE ≤ exit_high(eg. +0.05%)\n", "    \"\"\"\n", "    n = len(df)\n", "    pos  = np.zeros(n)    # +1 long, −1 short, 0 flat\n", "    fees = np.zeros(n)\n", "\n", "    for i in range(1,n):\n", "        lo, hi, cl = df[\"low\"].iat[i], df[\"high\"].iat[i], df[\"close\"].iat[i]\n", "        prev = pos[i-1]\n", "\n", "        if prev == 0:\n", "            if lo  <= ent_low:\n", "                pos[i]  =  1\n", "                fees[i] = 2*TAKER_FEE*CAPITAL_USD\n", "            elif hi >= ent_high:\n", "                pos[i]  = -1\n", "                fees[i] = 2*TAKER_FEE*CAPITAL_USD\n", "\n", "        elif prev == 1:\n", "            # holding long\n", "            if cl >= exit_low:\n", "                pos[i]  = 0\n", "                fees[i] = 2*TAKER_FEE*CAPITAL_USD\n", "            else:\n", "                pos[i]  = 1\n", "\n", "        else:  # prev == -1\n", "            if cl <= exit_high:\n", "                pos[i]  = 0\n", "                fees[i] = 2*TAKER_FEE*CAPITAL_USD\n", "            else:\n", "                pos[i]  = -1\n", "\n", "    # compute PnL & returns\n", "    delta   = df[\"close\"].diff().fillna(0).to_numpy()\n", "    usd_pnl = np.roll(pos, 1) * delta * CAPITAL_USD\n", "    net     = usd_pnl - fees\n", "    ret     = net / CAPITAL_USD\n", "    eq      = ret.cumsum()\n", "\n", "    # metrics\n", "    total_ret = eq[-1]*100\n", "    yrs       = len(ret)/(365*24*60)\n", "    ann_ret   = (1+eq[-1])**(1/yrs)-1\n", "    max_dd    = (eq - np.maximum.accumulate(eq)).min()*100\n", "    sharpe    = (ret.mean()/ret.std())*np.sqrt(365*24*60)\n", "\n", "    stats = {\n", "        \"Total %\": total_ret,\n", "        \"CAGR %\": ann_ret*100,\n", "        \"Max DD %\": max_dd,\n", "        \"<PERSON>\": sharpe\n", "    }\n", "    return stats, pd.Series(pos, index=df.index), pd.Series(eq, index=df.index)\n"]}, {"cell_type": "code", "execution_count": 13, "id": "4a19d7a3", "metadata": {}, "outputs": [], "source": ["# # %% 5 Run one example\n", "# # e.g. enter at ±0.20% , exit at ±0.05%\n", "# stats, pos_ser, eq_ser = backtest_4t(\n", "#     prem,\n", "#     ent_low  = -0.0020,\n", "#     ent_high =  0.0020,\n", "#     exit_low  = -0.0005,\n", "#     exit_high =  0.0005\n", "# )\n", "\n", "# print(\"Stats:\", {k:f\"{v:.2f}\" for k,v in stats.items()})\n", "\n", "# # plot equity\n", "# plt.figure(figsize=(10,4))\n", "# eq_ser.plot()\n", "# plt.title(\"Premium-Index 4T Strategy Equity\")\n", "# plt.ylabel(\"% PnL\"); plt.grid(True); plt.show()\n"]}, {"cell_type": "code", "execution_count": 14, "id": "1e8ff120", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running 81 parameter combinations…\n", "\n", "Entry-Lows  : [-0.001027 -0.000806 -0.000585]\n", "Entry-Highs : [5.100e-05 7.240e-04 1.396e-03]\n", "Exit-Lows   : [-0.000513 -0.000438 -0.000362]\n", "Exit-Highs  : [-3.62e-04 -2.20e-05  3.17e-04]\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "70f7d70152274ebf94fb7d56f0caa2b6", "version_major": 2, "version_minor": 0}, "text/plain": ["Entry Lows:   0%|          | 0/3 [00:00<?, ?step/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_24644\\2098197888.py:50: RuntimeWarning: invalid value encountered in scalar power\n", "  ann_ret   = (1+eq[-1])**(1/yrs)-1\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ent_low</th>\n", "      <th>ent_high</th>\n", "      <th>exit_low</th>\n", "      <th>exit_high</th>\n", "      <th>Total %</th>\n", "      <th>CAGR %</th>\n", "      <th>Max DD %</th>\n", "      <th><PERSON></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>-0.001027</td>\n", "      <td>0.001396</td>\n", "      <td>-0.000362</td>\n", "      <td>0.000317</td>\n", "      <td>-773.168285</td>\n", "      <td>NaN</td>\n", "      <td>-773.203784</td>\n", "      <td>-38.820980</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>-0.001027</td>\n", "      <td>0.001396</td>\n", "      <td>-0.000438</td>\n", "      <td>0.000317</td>\n", "      <td>-884.868394</td>\n", "      <td>NaN</td>\n", "      <td>-884.903893</td>\n", "      <td>-44.193781</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>-0.001027</td>\n", "      <td>0.001396</td>\n", "      <td>-0.000513</td>\n", "      <td>0.000317</td>\n", "      <td>-979.663949</td>\n", "      <td>NaN</td>\n", "      <td>-979.663949</td>\n", "      <td>-48.535741</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>-0.001027</td>\n", "      <td>0.000724</td>\n", "      <td>-0.000362</td>\n", "      <td>0.000317</td>\n", "      <td>-1439.710576</td>\n", "      <td>NaN</td>\n", "      <td>-1439.746075</td>\n", "      <td>-61.902027</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>-0.001027</td>\n", "      <td>0.000724</td>\n", "      <td>-0.000438</td>\n", "      <td>0.000317</td>\n", "      <td>-1550.907048</td>\n", "      <td>NaN</td>\n", "      <td>-1550.942547</td>\n", "      <td>-66.437457</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>-0.001027</td>\n", "      <td>0.000724</td>\n", "      <td>-0.000513</td>\n", "      <td>0.000317</td>\n", "      <td>-1646.813017</td>\n", "      <td>NaN</td>\n", "      <td>-1646.813017</td>\n", "      <td>-70.126832</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>-0.000806</td>\n", "      <td>0.001396</td>\n", "      <td>-0.000362</td>\n", "      <td>0.000317</td>\n", "      <td>-2697.590843</td>\n", "      <td>NaN</td>\n", "      <td>-2697.626342</td>\n", "      <td>-101.321496</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>-0.000806</td>\n", "      <td>0.000724</td>\n", "      <td>-0.000362</td>\n", "      <td>0.000317</td>\n", "      <td>-3340.727551</td>\n", "      <td>NaN</td>\n", "      <td>-3340.763050</td>\n", "      <td>-114.974621</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>-0.000806</td>\n", "      <td>0.001396</td>\n", "      <td>-0.000438</td>\n", "      <td>0.000317</td>\n", "      <td>-3545.630170</td>\n", "      <td>NaN</td>\n", "      <td>-3545.665669</td>\n", "      <td>-125.823733</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>-0.000806</td>\n", "      <td>0.000724</td>\n", "      <td>-0.000438</td>\n", "      <td>0.000317</td>\n", "      <td>-4189.718211</td>\n", "      <td>NaN</td>\n", "      <td>-4189.753710</td>\n", "      <td>-137.553069</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     ent_low  ent_high  exit_low  exit_high      Total %  CAGR %     Max DD %  \\\n", "5  -0.001027  0.001396 -0.000362   0.000317  -773.168285     NaN  -773.203784   \n", "4  -0.001027  0.001396 -0.000438   0.000317  -884.868394     NaN  -884.903893   \n", "3  -0.001027  0.001396 -0.000513   0.000317  -979.663949     NaN  -979.663949   \n", "2  -0.001027  0.000724 -0.000362   0.000317 -1439.710576     NaN -1439.746075   \n", "1  -0.001027  0.000724 -0.000438   0.000317 -1550.907048     NaN -1550.942547   \n", "0  -0.001027  0.000724 -0.000513   0.000317 -1646.813017     NaN -1646.813017   \n", "11 -0.000806  0.001396 -0.000362   0.000317 -2697.590843     NaN -2697.626342   \n", "8  -0.000806  0.000724 -0.000362   0.000317 -3340.727551     NaN -3340.763050   \n", "10 -0.000806  0.001396 -0.000438   0.000317 -3545.630170     NaN -3545.665669   \n", "7  -0.000806  0.000724 -0.000438   0.000317 -4189.718211     NaN -4189.753710   \n", "\n", "    <PERSON>  \n", "5   -38.820980  \n", "4   -44.193781  \n", "3   -48.535741  \n", "2   -61.902027  \n", "1   -66.437457  \n", "0   -70.126832  \n", "11 -101.321496  \n", "8  -114.974621  \n", "10 -125.823733  \n", "7  -137.553069  "]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# %% 6 ▸ Data-driven threshold ranges (no magic numbers)\n", "# ────────────────────────────────────────────────────────────────\n", "# Use quantiles of the premium series to set sensible bounds.\n", "from tqdm.notebook import tqdm\n", "\n", "# 1) grab the series\n", "low_vals   = prem[\"low\"]\n", "high_vals  = prem[\"high\"]\n", "close_vals = prem[\"close\"]\n", "\n", "# 2) compute quantile cut-points\n", "ent_low_min,  ent_low_max  = low_vals.quantile([0.01, 0.30]).values\n", "ent_high_min, ent_high_max = high_vals.quantile([0.70, 0.99]).values\n", "\n", "# For exits we’ll bracket around the median of the close\n", "exit_low_min,  exit_low_max  = close_vals.quantile([0.2, 0.5]).values\n", "exit_high_min, exit_high_max = close_vals.quantile([0.5, 0.8]).values\n", "\n", "# 3) build grids of thresholds\n", "ent_lows   = np.linspace(ent_low_min,  ent_low_max, 3)   # 5 steps from -1% tile to -10% tile\n", "ent_highs  = np.linspace(ent_high_min, ent_high_max, 3)  # 5 steps from +90% tile to +99% tile\n", "exit_lows  = np.linspace(exit_low_min,  exit_low_max, 3) # 3 steps between 25% & 50% of close\n", "exit_highs = np.linspace(exit_high_min, exit_high_max, 3) # 3 steps between 50% & 75% of close\n", "\n", "\n", "total_runs = (len(ent_lows) * len(ent_highs) *\n", "              len(exit_lows) * len(exit_highs))\n", "print(f\"Running {total_runs:,} parameter combinations…\\n\")\n", "print(\"Entry-Lows  :\", np.round(ent_lows, 6))\n", "print(\"Entry-Highs :\", np.round(ent_highs,6))\n", "print(\"Exit-Lows   :\", np.round(exit_lows, 6))\n", "print(\"Exit-Highs  :\", np.round(exit_highs,6))\n", "\n", "# %% 7 ▸ Grid-search with these data-driven ranges\n", "rows = []\n", "# outer progress bar over entry‐lows\n", "for el in tqdm(ent_lows, desc=\"Entry Lows\", unit=\"step\"):\n", "    for eh in ent_highs:\n", "        if el >= eh:\n", "            continue\n", "        # optional nested bar over exit‐lows\n", "        for xl in exit_lows:\n", "            for xh in exit_highs:\n", "                if not (el < xl <= 0 <= xh < eh):\n", "                    continue\n", "                stats, _, _ = backtest_4t(prem, el, eh, xl, xh)\n", "                rows.append({\n", "                    \"ent_low\":  el,\n", "                    \"ent_high\": eh,\n", "                    \"exit_low\": xl,\n", "                    \"exit_high\":xh,\n", "                    **stats\n", "                })\n", "\n", "grid_df = pd.DataFrame(rows).sort_values(\"<PERSON>\", ascending=False)\n", "grid_df.head(10)\n"]}, {"cell_type": "code", "execution_count": 17, "id": "70a47ef9", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_24644\\2098197888.py:50: RuntimeWarning: invalid value encountered in scalar power\n", "  ann_ret   = (1+eq[-1])**(1/yrs)-1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Performance for thresholds:\n", "  Entry Low  : -0.1027%\n", "  Entry High : 0.1396%\n", "  Exit Low   : -0.0362%\n", "  Exit High  : 0.0317%\n", "\n", "Total %        : -773.62\n", "CAGR %         : nan\n", "Max DD %       : -773.65\n", "<PERSON>     : -38.84\n"]}, {"data": {"image/png": "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**********************************************************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", "text/plain": ["<Figure size 1000x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# %% 8 ▸ Final run with chosen thresholds & performance display\n", "\n", "# 1) Set your four thresholds (example values here)\n", "ent_low,  ent_high  = -0.001027, 0.001396    # entry thresholds (LOW→long, HIGH→short)\n", "exit_low, exit_high = -0.000362, 0.000317    # exit thresholds (CLOSE→flat)\n", "\n", "# 2) Run the backtest\n", "stats, pos_ser, eq_ser = backtest_4t(\n", "    prem,\n", "    ent_low=ent_low, ent_high=ent_high,\n", "    exit_low=exit_low, exit_high=exit_high\n", ")\n", "\n", "# 3) Print out all key performance metrics\n", "print(\"Performance for thresholds:\")\n", "print(f\"  Entry Low  : {ent_low:.4%}\")\n", "print(f\"  Entry High : {ent_high:.4%}\")\n", "print(f\"  Exit Low   : {exit_low:.4%}\")\n", "print(f\"  Exit High  : {exit_high:.4%}\\n\")\n", "\n", "for metric, value in stats.items():\n", "    print(f\"{metric:15}: {value:,.2f}\")\n", "\n", "# 4) (Optional) Plot the equity curve\n", "import matplotlib.pyplot as plt\n", "\n", "plt.figure(figsize=(10,4))\n", "eq_ser.plot()\n", "plt.title(f\"Equity Curve | EL={ent_low:.4%}, EH={ent_high:.4%}, XL={exit_low:.4%}, XH={exit_high:.4%}\")\n", "plt.ylabel(\"% PnL on $10k\"); plt.grid(True); plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 16, "id": "cd0ff239", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'ret_pct' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[16], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMean 1-min return :\u001b[39m\u001b[38;5;124m\"\u001b[39m, ret_pct\u001b[38;5;241m.\u001b[39mmean())\n\u001b[0;32m      2\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mStd-dev 1-min ret :\u001b[39m\u001b[38;5;124m\"\u001b[39m, ret_pct\u001b[38;5;241m.\u001b[39mstd())\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mShare of zero bars:\u001b[39m\u001b[38;5;124m\"\u001b[39m, (ret_pct \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m)\u001b[38;5;241m.\u001b[39mmean())\n", "\u001b[1;31mNameError\u001b[0m: name 'ret_pct' is not defined"]}], "source": ["print(\"Mean 1-min return :\", ret_pct.mean())\n", "print(\"Std-dev 1-min ret :\", ret_pct.std())\n", "print(\"Share of zero bars:\", (ret_pct == 0).mean())\n"]}, {"cell_type": "code", "execution_count": null, "id": "80d39236", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sharpe  (all bars) : 1.9179175387272804\n", "Sharpe  (active)   : 1.9179175449015728\n", "Sharpe  (hourly)   : 3.237331507625488\n", "Sharpe  (equity)   : 1.9179184481016316\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_48372\\1495208884.py:5: FutureWarning: 'H' is deprecated and will be removed in a future version, please use 'h' instead.\n", "  hourly   = ret_pct.resample(\"1H\").sum()\n"]}], "source": ["# after net / CAPITAL_USD lines\n", "ret_pct = net / CAPITAL_USD\n", "\n", "active   = ret_pct[ret_pct != 0]\n", "hourly   = ret_pct.resample(\"1H\").sum()\n", "eq_curve = (1 + ret_pct).cumprod()\n", "\n", "def shr(series, scale):\n", "    return (series.mean() / series.std()) * np.sqrt(scale)\n", "\n", "print(\"<PERSON>  (all bars) :\", shr(ret_pct,   365*24*60))\n", "print(\"<PERSON>  (active)   :\", shr(active,   365*24*60 * active.size/len(ret_pct)))\n", "print(\"Sharpe  (hourly)   :\", shr(hourly,   365*24))\n", "print(\"Sharpe  (equity)   :\", shr(eq_curve.pct_change().dropna(), 365*24*60))\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}