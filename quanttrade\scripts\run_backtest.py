"""
Run Backtests for Cryptocurrency Trading Strategies

This script runs backtests for various trading strategies on cryptocurrency data
at different timeframes.
"""

import argparse
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import os
import sys
import webbrowser

from quanttrade.backtest.framework import BacktestFramework
from quanttrade.strategies import basic_pandas as basic, custom


def run_backtest(
    data_file: str,
    strategy_name: str,
    timeframe: str,
    start_date: str,
    end_date: str,
    initial_capital: float,
    commission: float,  # This is actually the trading_fee_rate
    **strategy_params,
):
    """
    Run a backtest with the specified parameters.

    Args:
        data_file: Path to the CSV file containing price data
        strategy_name: Name of the strategy to use
        timeframe: Timeframe for backtesting ('1m', '1h', '4h', '1d')
        start_date: Start date for backtesting (YYYY-MM-DD)
        end_date: End date for backtesting (YYYY-MM-DD)
        initial_capital: Initial capital for backtesting
        commission: Trading fee rate applied to both entry and exit (as a decimal)
        strategy_params: Additional parameters for the strategy
    """
    # Initialize backtest framework
    backtest = BacktestFramework(
        data_file=data_file,
        start_date=start_date,
        end_date=end_date,
        timeframe=timeframe,
        initial_capital=initial_capital,
        trading_fee_rate=commission,  # Pass commission as trading_fee_rate
    )

    # Select strategy function
    if strategy_name == "template":

        def strategy_func(df):
            return basic.strategy_template_pandas(df, **strategy_params)

    elif strategy_name == "custom":

        def strategy_func(df):
            return custom.custom_strategy_template(df, **strategy_params)

    else:
        print(f"Unknown strategy: {strategy_name}. Available strategies: 'template', 'custom'.")
        print(
            "All specific strategy implementations have been removed as they were unprofitable."
        )
        print("Use the template strategies to implement your own profitable strategies.")
        return

    # Run backtest
    print(f"\nRunning {strategy_name} strategy on {timeframe} timeframe...")
    results = backtest.run_backtest(strategy_func)

    # Plot results
    output_dir = f"backtest_results/{strategy_name}_{timeframe}"
    backtest.plot_results(save_dir=output_dir)

    # Generate TradingView-like HTML report
    html_path = backtest.generate_interactive_report(save_dir=output_dir)

    # Open the HTML report in the default browser
    if html_path:
        webbrowser.open(f"file://{os.path.abspath(html_path)}")

    return results


def main():
    """Main function to parse arguments and run backtests."""
    parser = argparse.ArgumentParser(description="Run cryptocurrency trading strategy backtests")

    parser.add_argument(
        "--data-file",
        type=str,
        default="quanttrade/data/datasets/btcusd_1_min_data.csv",
        help="Path to the CSV file containing price data",
    )

    parser.add_argument(
        "--strategy",
        type=str,
        default="template",
        choices=[
            "template",
            "custom",
        ],
        help="Trading strategy template to use",
    )

    parser.add_argument(
        "--timeframe",
        type=str,
        default="1m",
        choices=["1m", "4h", "1h", "1d"],
        help="Timeframe for backtesting",
    )

    parser.add_argument(
        "--start-date",
        type=str,
        default="2020-01-01",
        help="Start date for backtesting (YYYY-MM-DD)",
    )

    parser.add_argument(
        "--end-date", type=str, default="2025-01-27", help="End date for backtesting (YYYY-MM-DD)"
    )

    parser.add_argument(
        "--initial-capital", type=float, default=10000.0, help="Initial capital for backtesting"
    )

    parser.add_argument(
        "--trading-fee-rate",
        type=float,
        default=0.0005,
        help="Trading fee rate applied to both entry and exit (as a decimal)",
    )

    # Template strategy parameters (add your own parameters here)
    # parser.add_argument("--your-param", type=int, default=10, help="Your custom parameter")

    args = parser.parse_args()

    # Prepare strategy parameters (customize this for your strategy)
    strategy_params = {}
    # strategy_params = {"your_param": args.your_param}

    # Run backtest
    run_backtest(
        data_file=args.data_file,
        strategy_name=args.strategy,
        timeframe=args.timeframe,
        start_date=args.start_date,
        end_date=args.end_date,
        initial_capital=args.initial_capital,
        commission=args.trading_fee_rate,  # Using trading_fee_rate for the commission parameter
        **strategy_params,
    )


if __name__ == "__main__":
    main()
