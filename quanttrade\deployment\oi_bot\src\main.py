#!/usr/bin/env python
"""
Stateless OI/Price strategy.
Run once (e.g. every 5 min in Fargate).  For each coin in config.yaml:
    • ratio = open-interest-value / last-hour-close
    • if ratio > upper  ⇒ target SHORT (-1)
      if ratio < lower  ⇒ target LONG   (+1)
      else              ⇒ target FLAT    (0)
    • Reconcile target with real position; open/close as needed.
"""

import os, math, yaml, time
from datetime import datetime, timezone
from dotenv import load_dotenv
from loguru import logger

from utils import (  # helper functions you already have
    binance_client,
    mark_price,
    last_hour_close,
    oi_value,
    wallet_equity,
    get_current_position,
    close_position,
    tg_send,
    lot_step,
)

load_dotenv()  # picks up TG / API creds

# ── Load thresholds & sizing ───────────────────────────────────────────────
with open("config.yaml") as f:
    CFG = yaml.safe_load(f)

COINS = CFG["coins"]  # list of dicts
TELEGRAM_TOKEN = os.getenv("TG_BOT_TOKEN")
TELEGRAM_CHAT = os.getenv("TG_CHAT_ID")
BAR_SEC = CFG["bar_seconds"]
SAFETY = CFG["safety_delay_sec"]

logger.add("bot.log", rotation="7 days")
logger.info("Stateless OI/Price bot – single-shot run started")

cli = binance_client()
equity = wallet_equity(cli)  # total wallet USDT


# ── Helper -----------------------------------------------------------------
def qty_from_equity(cli, sym, equity_usd, price):
    step = lot_step(cli, sym)  # pulls once, then cached
    raw = equity_usd / price
    prec = int(abs(math.log10(step)))
    qty = math.floor(raw / step) * step  # round *down* to nearest step
    return round(qty, prec)


def decide_target(ratio: float, low: float, high: float) -> int:
    """Return 1 (long), -1 (short) or 0 (flat) based on thresholds."""
    if ratio > high:
        return -1
    if ratio < low:
        return 1
    return 0


def seconds_to_next_bar():
    srv = time.time()
    nxt = math.ceil(srv / BAR_SEC) * BAR_SEC
    return max(nxt - srv + SAFETY, 1)


# ─── Main loop ─────────────────────────────────────────────────────────────
while True:
    # wait until just after the bar closes
    print(seconds_to_next_bar())
    time.sleep(seconds_to_next_bar())

    # ── Core loop over all configured symbols ----------------------------------
    for coin in COINS:
        sym, low, high = coin["symbol"], coin["lower"], coin["upper"]
        lev = coin["leverage"]

        try:
            price = last_hour_close(cli, sym)  # previous 1-h candle close
            oi = oi_value(cli, sym)
            ratio = oi / price

            target = decide_target(ratio, low, high)
            cur = get_current_position(cli, sym)

            if target == cur:
                logger.info(f"{sym}: ratio {ratio:.4g} ⇒ already at target {target}")
                continue

            # ---- flatten anything we shouldn't hold ----
            if cur != 0:
                logger.info(f"{sym}: closing existing position {cur}")
                msg = close_position(cli, sym)
                tg_send(msg)

            if target == 0:
                logger.info(f"{sym}: now flat")
                continue

            # ---- open new position ------------------------------------------------
            notional = equity  # USD
            qty = qty_from_equity(cli, sym, notional, price)
            if qty == 0:
                logger.warning(f"{sym}: equity too small for minQty — skipping")
                continue
            side = "BUY" if target == 1 else "SELL"

            order = cli.new_order(symbol=sym, side=side, type="MARKET", quantity=qty)

            txt = (
                f"*{datetime.utcnow():%Y-%m-%d %H:%M}*  {sym}\n"
                f"Opened {'LONG' if target==1 else 'SHORT'}  qty `{qty}` @ `{price:.2f}`\n"
                f"ratio {ratio:.4g}   threshold [{low:.4g}, {high:.4g}]   Lev {lev}×"
            )
            logger.info(txt)
            tg_send(txt)

        except Exception as e:
            logger.exception(f"{sym}: error {e}")

    logger.info("Run finished ✔")
