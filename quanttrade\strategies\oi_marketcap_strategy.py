"""
Open Interest / Market Cap Ratio Trading Strategy

This strategy trades based on extremes in the Open Interest to Market Cap ratio:
- High OI/Market Cap ratio (>90th percentile) suggests overleveraged market → Short signal
- Low OI/Market Cap ratio (<10th percentile) suggests underleveraged market → Long signal
- Mean reversion approach with dynamic position sizing
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional


def calculate_oi_marketcap_ratio(df: pd.DataFrame, btc_supply: float = 19700000) -> pd.DataFrame:
    """
    Calculate Open Interest / Market Cap ratio.
    
    Args:
        df: DataFrame with OHLCV data
        btc_supply: Total BTC supply for market cap calculation
        
    Returns:
        DataFrame with OI/Market Cap ratio
    """
    df = df.copy()
    
    # For demonstration, create synthetic but realistic OI data
    # In practice, you would fetch this from derivatives exchanges
    np.random.seed(42)
    
    # Base OI around 500k BTC with correlation to price volatility
    base_oi = 500000
    
    # Calculate price volatility (20-period rolling std)
    df['price_volatility'] = df['Close'].rolling(window=20).std()
    vol_normalized = df['price_volatility'] / df['price_volatility'].rolling(window=100).mean()
    vol_normalized = vol_normalized.fillna(1.0)
    
    # OI tends to increase with volatility and price momentum
    price_momentum = df['Close'].pct_change(periods=10).fillna(0)
    momentum_factor = 1 + 0.3 * np.abs(price_momentum)
    volatility_factor = 1 + 0.5 * vol_normalized
    
    # Add some noise and trends
    noise = np.random.normal(1, 0.15, len(df))
    trend = np.linspace(0.8, 1.4, len(df))  # Gradual increase over time
    
    df['open_interest'] = base_oi * momentum_factor * volatility_factor * noise * trend
    df['open_interest'] = df['open_interest'].clip(lower=200000, upper=1200000)
    
    # Calculate market cap
    df['market_cap'] = df['Close'] * btc_supply
    
    # Calculate OI/Market Cap ratio
    df['oi_usd_value'] = df['open_interest'] * df['Close']
    df['oi_marketcap_ratio'] = df['oi_usd_value'] / df['market_cap']
    
    return df


def oi_marketcap_strategy(df: pd.DataFrame, **kwargs) -> pd.DataFrame:
    """
    Implement Open Interest / Market Cap ratio trading strategy.
    
    Args:
        df: DataFrame with OHLCV data
        **kwargs: Strategy parameters
        
    Returns:
        DataFrame with trading signals and metrics
    """
    # Strategy parameters
    lookback_period = kwargs.get('lookback_period', 100)  # Period for calculating percentiles
    high_threshold_pct = kwargs.get('high_threshold_pct', 90)  # 90th percentile
    low_threshold_pct = kwargs.get('low_threshold_pct', 10)   # 10th percentile
    exit_threshold_pct = kwargs.get('exit_threshold_pct', 50)  # 50th percentile (median)
    initial_capital = kwargs.get('initial_capital', 10000.0)
    trading_fee_rate = kwargs.get('trading_fee_rate', 0.0005)  # 0.05%
    position_size_pct = kwargs.get('position_size_pct', 0.95)  # Use 95% of capital
    
    # Calculate OI/Market Cap ratio
    df = calculate_oi_marketcap_ratio(df)
    
    # Calculate rolling percentiles for dynamic thresholds
    df['oi_ratio_high_threshold'] = df['oi_marketcap_ratio'].rolling(
        window=lookback_period, min_periods=20
    ).quantile(high_threshold_pct / 100)
    
    df['oi_ratio_low_threshold'] = df['oi_marketcap_ratio'].rolling(
        window=lookback_period, min_periods=20
    ).quantile(low_threshold_pct / 100)
    
    df['oi_ratio_exit_threshold'] = df['oi_marketcap_ratio'].rolling(
        window=lookback_period, min_periods=20
    ).quantile(exit_threshold_pct / 100)
    
    # Initialize strategy state
    df['signal'] = 0
    df['position'] = 0
    df['position_size'] = 0.0
    df['entry_price'] = 0.0
    df['cash'] = initial_capital
    df['equity'] = initial_capital
    df['unrealized_pnl'] = 0.0
    
    # Strategy implementation
    position = 0  # 0: flat, 1: long, -1: short
    position_size = 0.0
    entry_price = 0.0
    cash = initial_capital
    
    for i in range(1, len(df)):
        current_price = df['Close'].iloc[i]
        current_ratio = df['oi_marketcap_ratio'].iloc[i]
        high_thresh = df['oi_ratio_high_threshold'].iloc[i]
        low_thresh = df['oi_ratio_low_threshold'].iloc[i]
        exit_thresh = df['oi_ratio_exit_threshold'].iloc[i]
        
        signal = 0
        
        # Skip if thresholds are not available yet
        if pd.isna(high_thresh) or pd.isna(low_thresh) or pd.isna(exit_thresh):
            df.iloc[i, df.columns.get_loc('signal')] = signal
            df.iloc[i, df.columns.get_loc('position')] = position
            df.iloc[i, df.columns.get_loc('position_size')] = position_size
            df.iloc[i, df.columns.get_loc('entry_price')] = entry_price
            df.iloc[i, df.columns.get_loc('cash')] = cash
            df.iloc[i, df.columns.get_loc('equity')] = cash
            continue
        
        # Entry signals
        if position == 0:  # No position
            if current_ratio >= high_thresh:
                # High OI/Market Cap ratio → Market overleveraged → Short
                position_value = cash * position_size_pct
                position_size = position_value / current_price
                entry_fee = position_value * trading_fee_rate
                
                if cash >= entry_fee:
                    position = -1
                    entry_price = current_price
                    cash -= entry_fee
                    signal = -1
                    
            elif current_ratio <= low_thresh:
                # Low OI/Market Cap ratio → Market underleveraged → Long
                position_value = cash * position_size_pct
                position_size = position_value / current_price
                entry_fee = position_value * trading_fee_rate
                
                if cash >= entry_fee:
                    position = 1
                    entry_price = current_price
                    cash -= entry_fee
                    signal = 1
        
        # Exit signals
        elif position != 0:
            should_exit = False
            
            if position == 1:  # Long position
                # Exit long when ratio returns to median or goes to high extreme
                if current_ratio >= exit_thresh:
                    should_exit = True
            elif position == -1:  # Short position
                # Exit short when ratio returns to median or goes to low extreme
                if current_ratio <= exit_thresh:
                    should_exit = True
            
            if should_exit:
                # Close position
                exit_value = position_size * current_price
                exit_fee = exit_value * trading_fee_rate
                
                if position == 1:  # Closing long
                    pnl = (current_price - entry_price) * position_size - exit_fee
                else:  # Closing short
                    pnl = (entry_price - current_price) * position_size - exit_fee
                
                cash += exit_value - exit_fee
                position = 0
                position_size = 0.0
                entry_price = 0.0
                signal = 0  # Flat signal
        
        # Calculate current equity (mark-to-market)
        if position != 0:
            unrealized_pnl = position * (current_price - entry_price) * position_size
            # Account for potential exit fees
            potential_exit_fee = position_size * current_price * trading_fee_rate
            unrealized_pnl -= potential_exit_fee
            current_equity = cash + position_size * current_price + unrealized_pnl
        else:
            unrealized_pnl = 0.0
            current_equity = cash
        
        # Update DataFrame
        df.iloc[i, df.columns.get_loc('signal')] = signal
        df.iloc[i, df.columns.get_loc('position')] = position
        df.iloc[i, df.columns.get_loc('position_size')] = position_size
        df.iloc[i, df.columns.get_loc('entry_price')] = entry_price
        df.iloc[i, df.columns.get_loc('cash')] = cash
        df.iloc[i, df.columns.get_loc('equity')] = current_equity
        df.iloc[i, df.columns.get_loc('unrealized_pnl')] = unrealized_pnl
    
    return df


def calculate_strategy_metrics(df: pd.DataFrame, timeframe: str = "1d") -> Dict:
    """
    Calculate comprehensive strategy performance metrics.
    
    Args:
        df: DataFrame with strategy results
        timeframe: Data timeframe for annualization
        
    Returns:
        Dictionary with performance metrics
    """
    equity_curve = df['equity']
    initial_capital = equity_curve.iloc[0]
    final_equity = equity_curve.iloc[-1]
    
    # Basic metrics
    total_return = (final_equity / initial_capital - 1) * 100
    
    # Calculate returns
    returns = equity_curve.pct_change().dropna()
    
    # Annualization factors
    timeframe_periods = {
        "1m": 365 * 24 * 60,
        "1h": 365 * 24,
        "4h": 365 * 6,
        "1d": 365
    }
    
    periods_per_year = timeframe_periods.get(timeframe, 365)
    
    # Annualized return
    if len(equity_curve) > 1:
        annualized_return = ((final_equity / initial_capital) ** (periods_per_year / len(equity_curve)) - 1) * 100
    else:
        annualized_return = 0
    
    # Sharpe ratio
    if len(returns) > 1 and returns.std() > 0:
        sharpe_ratio = np.sqrt(periods_per_year) * returns.mean() / returns.std()
    else:
        sharpe_ratio = 0
    
    # Maximum drawdown
    rolling_max = equity_curve.expanding().max()
    drawdown = (equity_curve - rolling_max) / rolling_max
    max_drawdown = drawdown.min() * 100
    
    # Trade analysis
    signals = df['signal']
    total_trades = (signals != 0).sum()
    long_trades = (signals == 1).sum()
    short_trades = (signals == -1).sum()
    
    # Win rate analysis
    trade_returns = []
    position_changes = df['position'].diff()
    
    for i in range(1, len(df)):
        if position_changes.iloc[i] != 0 and df['position'].iloc[i-1] != 0:
            # Position closed
            entry_idx = None
            for j in range(i-1, -1, -1):
                if df['position'].iloc[j] == 0:
                    entry_idx = j + 1
                    break
            
            if entry_idx is not None:
                entry_equity = df['equity'].iloc[entry_idx]
                exit_equity = df['equity'].iloc[i]
                trade_return = (exit_equity - entry_equity) / entry_equity
                trade_returns.append(trade_return)
    
    if trade_returns:
        win_rate = (np.array(trade_returns) > 0).mean() * 100
        avg_win = np.mean([r for r in trade_returns if r > 0]) * 100 if any(r > 0 for r in trade_returns) else 0
        avg_loss = np.mean([r for r in trade_returns if r < 0]) * 100 if any(r < 0 for r in trade_returns) else 0
        profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')
    else:
        win_rate = 0
        avg_win = 0
        avg_loss = 0
        profit_factor = 0
    
    return {
        'initial_capital': initial_capital,
        'final_equity': final_equity,
        'total_return_pct': total_return,
        'annualized_return_pct': annualized_return,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown_pct': max_drawdown,
        'total_trades': total_trades,
        'long_trades': long_trades,
        'short_trades': short_trades,
        'win_rate_pct': win_rate,
        'avg_win_pct': avg_win,
        'avg_loss_pct': avg_loss,
        'profit_factor': profit_factor,
        'completed_trades': len(trade_returns)
    }
