#!/usr/bin/env python3
"""
RSI-Based Martingale Backtesting Script

This script implements a comprehensive RSI-based martingale strategy with pyramiding:
- Accepts CSV files or CCXT exchange data
- Implements 10% → 20% → 70% pyramiding on RSI reversions
- Tracks mark-to-market equity with 0.05% trading fees
- Outputs detailed performance metrics and equity curve

Usage:
    python rsi_martingale_backtest.py --csv data/BTCUSDT_1h.csv
    python rsi_martingale_backtest.py --exchange binance --symbol BTC/USDT --timeframe 1h --days 365
"""

import argparse
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path

# Add quanttrade to path
sys.path.append(str(Path(__file__).parent))

from quanttrade.strategies.rsi_martingale import rsi_martingale_strategy
from quanttrade.data.loader import load_crypto_data


def fetch_ccxt_data(exchange_name: str, symbol: str, timeframe: str, days: int = 365):
    """
    Fetch data from CCXT exchange.
    
    Args:
        exchange_name: Exchange name (e.g., 'binance', 'coinbase')
        symbol: Trading pair symbol (e.g., 'BTC/USDT')
        timeframe: Timeframe (e.g., '1h', '4h', '1d')
        days: Number of days to fetch
        
    Returns:
        DataFrame with OHLCV data
    """
    try:
        import ccxt
    except ImportError:
        raise ImportError("CCXT library not installed. Install with: pip install ccxt")
    
    # Initialize exchange
    exchange_class = getattr(ccxt, exchange_name)
    exchange = exchange_class()
    
    # Calculate start time
    end_time = datetime.now()
    start_time = end_time - timedelta(days=days)
    
    # Fetch OHLCV data
    print(f"Fetching {symbol} {timeframe} data from {exchange_name} for {days} days...")
    
    since = int(start_time.timestamp() * 1000)  # Convert to milliseconds
    ohlcv = exchange.fetch_ohlcv(symbol, timeframe, since=since)
    
    # Convert to DataFrame
    df = pd.DataFrame(ohlcv, columns=['Timestamp', 'Open', 'High', 'Low', 'Close', 'Volume'])
    df['Timestamp'] = pd.to_datetime(df['Timestamp'], unit='ms')
    
    print(f"Fetched {len(df)} records from {df['Timestamp'].min()} to {df['Timestamp'].max()}")
    return df


def calculate_performance_metrics(equity_curve: pd.Series, initial_capital: float) -> dict:
    """
    Calculate comprehensive performance metrics.
    
    Args:
        equity_curve: Equity curve as pandas Series
        initial_capital: Initial capital amount
        
    Returns:
        Dictionary with performance metrics
    """
    # Calculate returns
    returns = equity_curve.pct_change().dropna()
    
    # Basic metrics
    total_return = (equity_curve.iloc[-1] / initial_capital - 1) * 100
    
    # Annualized metrics (assuming hourly data)
    periods_per_year = 365 * 24  # Hours per year
    annualized_return = ((equity_curve.iloc[-1] / initial_capital) ** (periods_per_year / len(equity_curve)) - 1) * 100
    
    # Sharpe ratio (annualized)
    if len(returns) > 1 and returns.std() > 0:
        sharpe_ratio = np.sqrt(periods_per_year) * returns.mean() / returns.std()
    else:
        sharpe_ratio = 0
    
    # Maximum drawdown
    rolling_max = equity_curve.expanding().max()
    drawdown = (equity_curve - rolling_max) / rolling_max
    max_drawdown = drawdown.min() * 100
    
    # Win rate and other trade metrics
    positive_returns = returns[returns > 0]
    negative_returns = returns[returns < 0]
    
    win_rate = len(positive_returns) / len(returns) * 100 if len(returns) > 0 else 0
    avg_win = positive_returns.mean() * 100 if len(positive_returns) > 0 else 0
    avg_loss = negative_returns.mean() * 100 if len(negative_returns) > 0 else 0
    
    # Profit factor
    total_wins = positive_returns.sum() if len(positive_returns) > 0 else 0
    total_losses = abs(negative_returns.sum()) if len(negative_returns) > 0 else 0
    profit_factor = total_wins / total_losses if total_losses > 0 else float('inf')
    
    return {
        'total_return_pct': total_return,
        'annualized_return_pct': annualized_return,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown_pct': max_drawdown,
        'win_rate_pct': win_rate,
        'avg_win_pct': avg_win,
        'avg_loss_pct': avg_loss,
        'profit_factor': profit_factor,
        'total_trades': len(returns),
        'final_equity': equity_curve.iloc[-1],
        'initial_capital': initial_capital
    }


def plot_results(df: pd.DataFrame, output_dir: str = "backtest_results"):
    """
    Create comprehensive plots of backtest results.
    
    Args:
        df: DataFrame with backtest results
        output_dir: Directory to save plots
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # Create subplots
    fig, axes = plt.subplots(4, 1, figsize=(15, 16))
    
    # Plot 1: Price and RSI
    ax1 = axes[0]
    ax1.plot(df['Timestamp'], df['Close'], label='Price', color='black', linewidth=1)
    ax1.set_ylabel('Price', color='black')
    ax1.tick_params(axis='y', labelcolor='black')
    
    # Add RSI on secondary y-axis
    ax1_twin = ax1.twinx()
    ax1_twin.plot(df['Timestamp'], df['RSI'], label='RSI', color='purple', alpha=0.7)
    ax1_twin.axhline(y=30, color='green', linestyle='--', alpha=0.5, label='Oversold (30)')
    ax1_twin.axhline(y=70, color='red', linestyle='--', alpha=0.5, label='Overbought (70)')
    ax1_twin.axhline(y=50, color='blue', linestyle='--', alpha=0.5, label='Middle (50)')
    ax1_twin.set_ylabel('RSI', color='purple')
    ax1_twin.tick_params(axis='y', labelcolor='purple')
    ax1_twin.set_ylim(0, 100)
    
    ax1.set_title('Price and RSI')
    ax1.legend(loc='upper left')
    ax1_twin.legend(loc='upper right')
    
    # Plot 2: Position tracking
    ax2 = axes[1]
    ax2.plot(df['Timestamp'], df['position_side'], label='Position Side', color='blue', linewidth=2)
    ax2.plot(df['Timestamp'], df['pyramid_level'], label='Pyramid Level', color='orange', linewidth=2)
    ax2.set_ylabel('Position/Pyramid Level')
    ax2.set_title('Position and Pyramid Tracking')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # Plot 3: Equity curve
    ax3 = axes[2]
    ax3.plot(df['Timestamp'], df['equity_curve'], label='Equity Curve', color='green', linewidth=2)
    ax3.set_ylabel('Equity ($)')
    ax3.set_title('Equity Curve')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # Plot 4: Cash and position size
    ax4 = axes[3]
    ax4.plot(df['Timestamp'], df['cash'], label='Cash', color='blue', linewidth=1)
    ax4_twin = ax4.twinx()
    ax4_twin.plot(df['Timestamp'], df['position_size'], label='Position Size', color='red', linewidth=1)
    ax4.set_ylabel('Cash ($)', color='blue')
    ax4_twin.set_ylabel('Position Size', color='red')
    ax4.set_xlabel('Time')
    ax4.set_title('Cash and Position Size')
    ax4.legend(loc='upper left')
    ax4_twin.legend(loc='upper right')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f"{output_dir}/rsi_martingale_analysis.png", dpi=300, bbox_inches='tight')
    plt.show()


def main():
    parser = argparse.ArgumentParser(description='RSI-Based Martingale Backtesting Script')
    
    # Data source options
    data_group = parser.add_mutually_exclusive_group(required=True)
    data_group.add_argument('--csv', type=str, help='Path to CSV file with OHLCV data')
    data_group.add_argument('--exchange', type=str, help='CCXT exchange name (e.g., binance)')
    
    # CCXT options
    parser.add_argument('--symbol', type=str, help='Trading pair symbol (e.g., BTC/USDT)')
    parser.add_argument('--timeframe', type=str, default='1h', help='Timeframe (default: 1h)')
    parser.add_argument('--days', type=int, default=365, help='Number of days to fetch (default: 365)')
    
    # Strategy parameters
    parser.add_argument('--initial-capital', type=float, default=10000.0, help='Initial capital (default: 10000)')
    parser.add_argument('--trading-fee', type=float, default=0.0005, help='Trading fee rate (default: 0.0005)')
    parser.add_argument('--rsi-period', type=int, default=14, help='RSI period (default: 14)')
    parser.add_argument('--rsi-oversold', type=int, default=30, help='RSI oversold level (default: 30)')
    parser.add_argument('--rsi-overbought', type=int, default=70, help='RSI overbought level (default: 70)')
    
    # Output options
    parser.add_argument('--output-dir', type=str, default='backtest_results', help='Output directory')
    parser.add_argument('--save-csv', action='store_true', help='Save results to CSV')
    parser.add_argument('--no-plot', action='store_true', help='Skip plotting')
    
    args = parser.parse_args()
    
    # Validate CCXT arguments
    if args.exchange and not args.symbol:
        parser.error("--symbol is required when using --exchange")
    
    print("=" * 60)
    print("RSI-Based Martingale Backtesting Script")
    print("=" * 60)
    
    # Load data
    if args.csv:
        print(f"Loading data from CSV: {args.csv}")
        df = load_crypto_data(args.csv)
    else:
        print(f"Fetching data from {args.exchange}")
        df = fetch_ccxt_data(args.exchange, args.symbol, args.timeframe, args.days)
    
    print(f"Data loaded: {len(df)} records from {df['Timestamp'].min()} to {df['Timestamp'].max()}")
    
    # Run strategy
    print("\nRunning RSI Martingale strategy...")
    strategy_params = {
        'rsi_period': args.rsi_period,
        'rsi_oversold': args.rsi_oversold,
        'rsi_overbought': args.rsi_overbought,
        'initial_capital': args.initial_capital,
        'trading_fee_rate': args.trading_fee
    }
    
    df_result = rsi_martingale_strategy(df, **strategy_params)
    
    # Calculate performance metrics
    equity_curve = pd.Series(df_result['equity_curve'].values, index=df_result['Timestamp'])
    metrics = calculate_performance_metrics(equity_curve, args.initial_capital)
    
    # Print results
    print("\n" + "=" * 60)
    print("BACKTEST RESULTS")
    print("=" * 60)
    print(f"Initial Capital:      ${metrics['initial_capital']:,.2f}")
    print(f"Final Equity:         ${metrics['final_equity']:,.2f}")
    print(f"Total Return:         {metrics['total_return_pct']:.2f}%")
    print(f"Annualized Return:    {metrics['annualized_return_pct']:.2f}%")
    print(f"Sharpe Ratio:         {metrics['sharpe_ratio']:.2f}")
    print(f"Maximum Drawdown:     {metrics['max_drawdown_pct']:.2f}%")
    print(f"Win Rate:             {metrics['win_rate_pct']:.2f}%")
    print(f"Average Win:          {metrics['avg_win_pct']:.2f}%")
    print(f"Average Loss:         {metrics['avg_loss_pct']:.2f}%")
    print(f"Profit Factor:        {metrics['profit_factor']:.2f}")
    print(f"Total Periods:        {metrics['total_trades']:,}")
    
    # Save results
    os.makedirs(args.output_dir, exist_ok=True)
    
    if args.save_csv:
        csv_path = f"{args.output_dir}/rsi_martingale_results.csv"
        df_result.to_csv(csv_path, index=False)
        print(f"\nResults saved to: {csv_path}")
        
        # Save equity curve separately
        equity_path = f"{args.output_dir}/rsi_martingale_equity_curve.csv"
        equity_curve.to_csv(equity_path, header=['equity'])
        print(f"Equity curve saved to: {equity_path}")
    
    # Create plots
    if not args.no_plot:
        print("\nGenerating plots...")
        plot_results(df_result, args.output_dir)
        print(f"Plots saved to: {args.output_dir}/rsi_martingale_analysis.png")
    
    print("\nBacktest completed successfully!")


if __name__ == "__main__":
    main()
