# QuantTrade

A comprehensive framework for quantitative trading and backtesting cryptocurrency strategies.

## Features

-   Backtest trading strategies on 1-minute, 4-hour, and daily timeframes
-   Calculate performance metrics: PnL, equity curve, Sharpe ratio, max drawdown
-   Visualize results with charts and summary statistics
-   Compare strategy performance across different timeframes
-   Includes several pre-built rule-based strategies

## Quick Start Guide

### 1. Setup Environment

#### Using uv (recommended)

```bash
# Install uv if you don't have it
pip install uv

# Create and activate a virtual environment
uv venv .venv
# On Windows:
.venv\Scripts\activate
# On macOS/Linux:
source .venv/bin/activate

# Install the package in development mode
uv pip install -e .
```

#### Using pip

```bash
# Create and activate a virtual environment
python -m venv .venv
# On Windows:
.venv\Scripts\activate
# On macOS/Linux:
source .venv/bin/activate

# Install the package in development mode
pip install -e .
```

### 2. Running Scripts

There are three main ways to run the scripts:

#### Option 1: Using the installed command-line scripts

After installing the package, you can use these commands from anywhere:

```bash
# Run a single backtest with template strategy
backtest --data-file quanttrade/data/datasets/btcusd_1_min_data.csv --strategy template --timeframe 1d

# Run backtests on multiple timeframes with custom strategy
multi-backtest --data-file quanttrade/data/datasets/btcusd_1_min_data.csv --strategy custom

# Analyze data
analyze-data --file quanttrade/data/datasets/btcusd_1_min_data.csv

# Download cryptocurrency data
download-crypto --exchange binance --symbol BTC/USDT --timeframe 1d --start_date 2020-01-01
```

#### Option 2: Using Python module syntax

```bash
# Run a single backtest
python -m quanttrade.scripts.run_backtest --data-file quanttrade/data/datasets/btcusd_1_min_data.csv --strategy template --timeframe 1d

# Run backtests on multiple timeframes
python -m quanttrade.scripts.run_multi_timeframe_backtest --data-file quanttrade/data/datasets/btcusd_1_min_data.csv --strategy custom

# Analyze data
python -m quanttrade.scripts.analyze_data --file quanttrade/data/datasets/btcusd_1_min_data.csv
```

#### Option 3: Running the script files directly

```bash
# Run a single backtest
python quanttrade/scripts/run_backtest.py --data-file quanttrade/data/datasets/btcusd_1_min_data.csv --strategy template --timeframe 1d

# Run backtests on multiple timeframes
python quanttrade/scripts/run_multi_timeframe_backtest.py --data-file quanttrade/data/datasets/btcusd_1_min_data.csv --strategy custom

# Analyze data
python quanttrade/scripts/analyze_data.py --file quanttrade/data/datasets/btcusd_1_min_data.csv
```

## Available Strategy Templates

**⚠️ Important Note**: All specific strategy implementations have been removed as they were unprofitable. The framework now provides templates for you to implement your own profitable strategies.

1. **Template Strategy (`template`)**

    - Description: A basic template using pandas for technical indicators. Implement your own strategy logic here.
    - Location: `quanttrade/strategies/basic_pandas.py` - `strategy_template_pandas()`
    - Example:
        ```bash
        backtest --strategy template --timeframe 1d
        ```

2. **Custom Strategy Template (`custom`)**

    - Description: A more advanced template for custom strategies with helper functions for technical analysis.
    - Location: `quanttrade/strategies/custom.py` - `custom_strategy_template()`
    - Example:
        ```bash
        backtest --strategy custom --timeframe 1d
        ```

### How to Implement Your Own Strategy

1. **Edit the template files**: Modify `quanttrade/strategies/basic_pandas.py` or `quanttrade/strategies/custom.py`
2. **Add your logic**: Implement your buy/sell conditions in the template functions
3. **Add technical indicators**: Use the provided helper functions or create your own
4. **Test your strategy**: Run backtests to evaluate performance

Example template structure:

```python
def your_strategy(df: pd.DataFrame, **kwargs) -> pd.DataFrame:
    # Add technical indicators
    df['SMA20'] = df['Close'].rolling(window=20).mean()
    df['RSI14'] = calculate_rsi_pandas(df['Close'], period=14)

    # Initialize signal column
    df['signal'] = 0

    # Implement your strategy logic
    for i in range(1, len(df)):
        if your_buy_condition:
            df.loc[df.index[i], 'signal'] = 1  # Buy
        elif your_sell_condition:
            df.loc[df.index[i], 'signal'] = -1  # Sell

    return df
```

## Common Command-Line Options

All backtest scripts support these common options:

```
--data-file PATH       Path to the CSV file containing price data
--timeframe {1m,4h,1d} Timeframe for backtesting
--start-date DATE      Start date for backtesting (YYYY-MM-DD)
--end-date DATE        End date for backtesting (YYYY-MM-DD)
--initial-capital AMOUNT Initial capital for backtesting
--commission RATE      Commission rate for trades (as a decimal)
```

## Example Workflows

### Basic Workflow

1. Analyze your data to understand its characteristics:

    ```bash
    analyze-data --file quanttrade/data/datasets/btcusd_1_min_data.csv
    ```

2. Run a backtest with a template strategy:

    ```bash
    backtest --data-file quanttrade/data/datasets/btcusd_1_min_data.csv --strategy template --timeframe 4h --start-date 2020-01-01 --end-date 2023-01-01
    ```

3. Compare performance across different timeframes:
    ```bash
    multi-backtest --data-file quanttrade/data/datasets/btcusd_1_min_data.csv --strategy custom --start-date 2020-01-01 --end-date 2023-01-01
    ```

### Strategy Development Workflow

1. **Implement your strategy**: Edit the template functions in the strategy files
2. **Test your strategy**: Run backtests to evaluate performance
3. **Optimize parameters**: Modify your strategy parameters and test again
4. **Compare timeframes**: Use multi-backtest to see how your strategy performs across different timeframes

## Project Structure

```
quanttrade/
├── backtest/         # Backtesting framework
│   └── framework.py  # Core backtesting engine
├── data/             # Data loading and processing utilities
│   ├── datasets/     # Data files
│   └── loader.py     # Data loading functions
├── strategies/       # Trading strategy templates
│   ├── basic.py      # Basic strategy templates
│   ├── basic_pandas.py # Pandas-based strategy templates
│   └── custom.py     # Custom strategy templates
├── utils/            # Utility functions
│   ├── trade.py      # Trading utilities
│   └── trend_identifier.py # Trend identification utilities
├── scripts/          # Command-line scripts
│   ├── analyze_data.py               # Data analysis script
│   ├── download_crypto_data.py       # Cryptocurrency data download script
│   ├── identify_trend.py             # Trend identification script
│   ├── run_backtest.py               # Single backtest script

│   ├── run_multi_timeframe_backtest.py  # Multi-timeframe backtest script
│   └── visualize_trend_chart.py      # Trend visualization script
└── notebooks/        # Jupyter notebooks for analysis
    ├── Algorithmic_Trading_Machine_Learning_Quant_Strategies.ipynb
    ├── CTREND_Strategy.ipynb         # CTREND strategy implementation
    └── Kmean.ipynb                   # K-means clustering analysis
```

## Creating Your Own Strategy

To create your own strategy, modify the template functions in the strategy files:

### Option 1: Basic Template (Recommended for beginners)

Edit `quanttrade/strategies/basic_pandas.py` and modify the `strategy_template_pandas()` function:

```python
def strategy_template_pandas(df: pd.DataFrame, **kwargs) -> pd.DataFrame:
    df = df.copy()
    df["signal"] = 0

    # Add your technical indicators
    df['SMA20'] = df['Close'].rolling(window=20).mean()
    df['SMA50'] = df['Close'].rolling(window=50).mean()
    df['RSI14'] = calculate_rsi_pandas(df['Close'], period=14)

    # Implement your strategy logic
    for i in range(1, len(df)):
        # Example: Buy when SMA20 crosses above SMA50 and RSI is not overbought
        if (df['SMA20'].iloc[i] > df['SMA50'].iloc[i] and
            df['SMA20'].iloc[i-1] <= df['SMA50'].iloc[i-1] and
            df['RSI14'].iloc[i] < 70):
            df.loc[df.index[i], "signal"] = 1  # Buy signal

        # Example: Sell when SMA20 crosses below SMA50 or RSI is overbought
        elif (df['SMA20'].iloc[i] < df['SMA50'].iloc[i] and
              df['SMA20'].iloc[i-1] >= df['SMA50'].iloc[i-1]) or df['RSI14'].iloc[i] > 80:
            df.loc[df.index[i], "signal"] = -1  # Sell signal

    return df
```

### Option 2: Advanced Template

Edit `quanttrade/strategies/custom.py` and modify the `custom_strategy_template()` function for more complex strategies.

Then run your strategy:

```bash
backtest --strategy template --timeframe 4h  # For basic template
# or
backtest --strategy custom --timeframe 4h    # For advanced template
```

## Understanding Backtest Results

After running a backtest, results are saved in the `backtest_results` directory:

-   **Equity Curve**: Shows how your capital changes over time
-   **Drawdown Chart**: Shows the percentage decline from peak equity
-   **Trade PnL Chart**: Shows the profit/loss for each trade
-   **Performance Summary**: Text file with key metrics like Sharpe ratio, total PnL, etc.

For multi-timeframe backtests, a comparison of results across timeframes is also generated.

## Troubleshooting

-   **ImportError**: Make sure you've activated your virtual environment and installed the package with `uv pip install -e .` or `pip install -e .`
-   **FileNotFoundError**: Check that you're using the correct path to the data file
-   **No trades generated**: Ensure your strategy parameters are appropriate for the timeframe you're testing

## Working with Large Data Files

The repository is configured to exclude large data files from version control. To work with the project:

1. **Download data files separately**: Large data files like `btcusd_1_min_data.csv` should be downloaded separately and placed in the `quanttrade/data/datasets/` directory.

2. **Data sources**: You can obtain cryptocurrency data using the included download script:

    ```bash
    # Download daily BTC/USDT data from Binance
    download-crypto --exchange binance --symbol BTC/USDT --timeframe 1d --start_date 2020-01-01
    ```

    Or from these external sources:

    - [CryptoDataDownload](https://www.cryptodatadownload.com/data/)
    - [Kaggle Cryptocurrency Datasets](https://www.kaggle.com/datasets?search=cryptocurrency)
    - [Binance Historical Data](https://data.binance.vision/)

3. **Expected data format**: The scripts expect CSV files with at least these columns:

    - `timestamp` or `date`: Date and time of the candle
    - `open`: Opening price
    - `high`: Highest price in the period
    - `low`: Lowest price in the period
    - `close`: Closing price
    - `volume`: Trading volume

4. **Data preprocessing**: If your data is in a different format, you may need to preprocess it before using with the backtesting scripts.

## License

MIT
