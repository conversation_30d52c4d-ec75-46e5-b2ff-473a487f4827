#!/usr/bin/env python3
"""
Test Open Interest / Market Cap Strategy

This script backtests the OI/Market Cap ratio strategy and analyzes its profitability
and risk-adjusted returns (Sharpe ratio).
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict
import warnings

warnings.filterwarnings("ignore")

# Add quanttrade to path
sys.path.append(str(Path(__file__).parent))

from quanttrade.strategies.oi_marketcap_strategy import (
    oi_marketcap_strategy,
    calculate_strategy_metrics,
)
from quanttrade.data.loader import load_crypto_data, resample_ohlcv


def run_oi_strategy_backtest(
    timeframe: str = "1d",
    start_date: str = "2022-01-01",
    end_date: str = "2024-12-31",
    **strategy_params,
):
    """
    Run comprehensive backtest of OI/Market Cap strategy.

    Args:
        timeframe: Data timeframe ('1d', '4h', '1h')
        start_date: Start date for backtest
        end_date: End date for backtest
        **strategy_params: Strategy parameters

    Returns:
        DataFrame with backtest results
    """
    print("=" * 70)
    print("OPEN INTEREST / MARKET CAP STRATEGY BACKTEST")
    print("=" * 70)

    # Load BTC data
    try:
        print(f"Loading BTC data for {timeframe} timeframe...")
        df = load_crypto_data(
            "quanttrade/data/datasets/btcusd_1_min_data.csv",
            start_date=start_date,
            end_date=end_date,
        )

        if timeframe != "1m":
            df = resample_ohlcv(df, timeframe=timeframe)

        print(f"Loaded {len(df)} records from {df['Timestamp'].min()} to {df['Timestamp'].max()}")

    except Exception as e:
        print(f"Error loading data: {e}")
        return None

    # Default strategy parameters
    default_params = {
        "lookback_period": 100,
        "high_threshold_pct": 85,  # 85th percentile for shorts
        "low_threshold_pct": 15,  # 15th percentile for longs
        "exit_threshold_pct": 50,  # 50th percentile for exits
        "initial_capital": 10000.0,
        "trading_fee_rate": 0.0005,
        "position_size_pct": 0.90,  # Use 90% of capital
    }

    # Update with provided parameters
    default_params.update(strategy_params)

    print(f"\nStrategy Parameters:")
    for key, value in default_params.items():
        print(f"  {key}: {value}")

    # Run strategy
    print(f"\nExecuting OI/Market Cap strategy...")
    df_result = oi_marketcap_strategy(df, **default_params)

    # Calculate performance metrics
    metrics = calculate_strategy_metrics(df_result, timeframe=timeframe)

    return df_result, metrics


def create_strategy_analysis_plots(
    df: pd.DataFrame, metrics: Dict, save_dir: str = "backtest_results"
):
    """Create comprehensive analysis plots for the strategy."""

    os.makedirs(save_dir, exist_ok=True)

    fig, axes = plt.subplots(5, 1, figsize=(16, 20))

    # Plot 1: Price and OI/Market Cap Ratio
    ax1 = axes[0]
    ax1.plot(df["Timestamp"], df["Close"], color="black", linewidth=1.5, label="BTC Price")
    ax1.set_ylabel("BTC Price ($)", fontsize=12)
    ax1.set_title("BTC Price vs OI/Market Cap Strategy Signals", fontsize=14, fontweight="bold")

    # Mark trade signals
    buy_signals = df[df["signal"] == 1]
    sell_signals = df[df["signal"] == -1]

    if len(buy_signals) > 0:
        ax1.scatter(
            buy_signals["Timestamp"],
            buy_signals["Close"],
            color="green",
            marker="^",
            s=60,
            label="Long Entry",
            alpha=0.8,
            zorder=5,
        )
    if len(sell_signals) > 0:
        ax1.scatter(
            sell_signals["Timestamp"],
            sell_signals["Close"],
            color="red",
            marker="v",
            s=60,
            label="Short Entry",
            alpha=0.8,
            zorder=5,
        )

    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # Add OI/Market Cap ratio on secondary axis
    ax1_twin = ax1.twinx()
    ax1_twin.plot(
        df["Timestamp"],
        df["oi_marketcap_ratio"] * 100,
        color="purple",
        alpha=0.7,
        linewidth=1,
        label="OI/Market Cap %",
    )
    ax1_twin.plot(
        df["Timestamp"],
        df["oi_ratio_high_threshold"] * 100,
        color="red",
        linestyle="--",
        alpha=0.5,
        label="High Threshold",
    )
    ax1_twin.plot(
        df["Timestamp"],
        df["oi_ratio_low_threshold"] * 100,
        color="green",
        linestyle="--",
        alpha=0.5,
        label="Low Threshold",
    )
    ax1_twin.set_ylabel("OI/Market Cap Ratio (%)", color="purple")
    ax1_twin.legend(loc="upper right")

    # Plot 2: Position Tracking
    ax2 = axes[1]
    ax2.plot(df["Timestamp"], df["position"], color="blue", linewidth=2, label="Position")
    ax2.set_ylabel("Position (1=Long, -1=Short, 0=Flat)")
    ax2.set_title("Position Tracking Over Time", fontsize=14, fontweight="bold")
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(-1.5, 1.5)

    # Plot 3: Equity Curve
    ax3 = axes[2]
    ax3.plot(df["Timestamp"], df["equity"], color="green", linewidth=2.5, label="Strategy Equity")
    ax3.axhline(
        y=df["equity"].iloc[0], color="red", linestyle="--", alpha=0.7, label="Initial Capital"
    )

    # Add buy & hold comparison
    initial_price = df["Close"].iloc[0]
    final_price = df["Close"].iloc[-1]
    buy_hold_multiplier = df["Close"] / initial_price
    buy_hold_equity = df["equity"].iloc[0] * buy_hold_multiplier
    ax3.plot(
        df["Timestamp"],
        buy_hold_equity,
        color="orange",
        linewidth=2,
        alpha=0.7,
        label="Buy & Hold",
    )

    ax3.set_ylabel("Equity ($)")
    ax3.set_title("Strategy Performance vs Buy & Hold", fontsize=14, fontweight="bold")
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # Plot 4: Drawdown
    rolling_max = df["equity"].expanding().max()
    drawdown = (df["equity"] - rolling_max) / rolling_max * 100

    ax4 = axes[3]
    ax4.fill_between(df["Timestamp"], drawdown, 0, color="red", alpha=0.3, label="Drawdown")
    ax4.plot(df["Timestamp"], drawdown, color="red", linewidth=1)
    ax4.set_ylabel("Drawdown (%)")
    ax4.set_title("Strategy Drawdown Analysis", fontsize=14, fontweight="bold")
    ax4.legend()
    ax4.grid(True, alpha=0.3)

    # Plot 5: Rolling Sharpe Ratio (if enough data)
    if len(df) > 60:
        returns = df["equity"].pct_change().fillna(0)
        rolling_sharpe = returns.rolling(window=60).apply(
            lambda x: np.sqrt(365) * x.mean() / x.std() if x.std() > 0 else 0
        )

        ax5 = axes[4]
        ax5.plot(
            df["Timestamp"],
            rolling_sharpe,
            color="blue",
            linewidth=2,
            label="60-Period Rolling Sharpe",
        )
        ax5.axhline(y=1.0, color="green", linestyle="--", alpha=0.7, label="Sharpe = 1.0")
        ax5.axhline(y=0.0, color="red", linestyle="-", alpha=0.5)
        ax5.set_ylabel("Rolling Sharpe Ratio")
        ax5.set_xlabel("Time")
        ax5.set_title("Rolling Sharpe Ratio (60-Period)", fontsize=14, fontweight="bold")
        ax5.legend()
        ax5.grid(True, alpha=0.3)

    plt.tight_layout()

    # Save plot
    plot_path = f"{save_dir}/oi_marketcap_strategy_analysis.png"
    plt.savefig(plot_path, dpi=300, bbox_inches="tight")
    print(f"Analysis plots saved to: {plot_path}")

    plt.show()


def print_strategy_results(metrics: Dict, df: pd.DataFrame):
    """Print comprehensive strategy results."""

    print("\n" + "=" * 70)
    print("STRATEGY PERFORMANCE RESULTS")
    print("=" * 70)

    # Calculate buy & hold comparison
    initial_price = df["Close"].iloc[0]
    final_price = df["Close"].iloc[-1]
    buy_hold_return = (final_price / initial_price - 1) * 100

    print(f"\n💰 FINANCIAL PERFORMANCE")
    print(f"Initial Capital:        ${metrics['initial_capital']:,.2f}")
    print(f"Final Equity:           ${metrics['final_equity']:,.2f}")
    print(f"Total Return:           {metrics['total_return_pct']:.2f}%")
    print(f"Annualized Return:      {metrics['annualized_return_pct']:.2f}%")
    print(f"Buy & Hold Return:      {buy_hold_return:.2f}%")
    print(f"Excess Return:          {metrics['total_return_pct'] - buy_hold_return:.2f}%")

    print(f"\n📊 RISK METRICS")
    print(f"Sharpe Ratio:           {metrics['sharpe_ratio']:.3f}")
    print(f"Maximum Drawdown:       {metrics['max_drawdown_pct']:.2f}%")

    # Sharpe ratio interpretation
    if metrics["sharpe_ratio"] > 2.0:
        sharpe_rating = "🟢 EXCELLENT"
    elif metrics["sharpe_ratio"] > 1.0:
        sharpe_rating = "🟡 GOOD"
    elif metrics["sharpe_ratio"] > 0.5:
        sharpe_rating = "🟠 FAIR"
    else:
        sharpe_rating = "🔴 POOR"

    print(f"Sharpe Rating:          {sharpe_rating}")

    print(f"\n📈 TRADING ACTIVITY")
    print(f"Total Signals:          {metrics['total_trades']}")
    print(f"Long Entries:           {metrics['long_trades']}")
    print(f"Short Entries:          {metrics['short_trades']}")
    print(f"Completed Trades:       {metrics['completed_trades']}")

    if metrics["completed_trades"] > 0:
        print(f"Win Rate:               {metrics['win_rate_pct']:.1f}%")
        print(f"Average Win:            {metrics['avg_win_pct']:.2f}%")
        print(f"Average Loss:           {metrics['avg_loss_pct']:.2f}%")
        print(f"Profit Factor:          {metrics['profit_factor']:.2f}")

    # Strategy assessment
    print(f"\n🎯 STRATEGY ASSESSMENT")

    is_profitable = metrics["total_return_pct"] > 0
    beats_buy_hold = metrics["total_return_pct"] > buy_hold_return
    good_sharpe = metrics["sharpe_ratio"] > 1.0
    low_drawdown = abs(metrics["max_drawdown_pct"]) < 20

    print(f"Profitable:             {'✅ YES' if is_profitable else '❌ NO'}")
    print(f"Beats Buy & Hold:       {'✅ YES' if beats_buy_hold else '❌ NO'}")
    print(f"Good Sharpe Ratio:      {'✅ YES' if good_sharpe else '❌ NO'}")
    print(f"Low Drawdown (<20%):    {'✅ YES' if low_drawdown else '❌ NO'}")

    # Overall rating
    score = sum([is_profitable, beats_buy_hold, good_sharpe, low_drawdown])
    if score >= 3:
        overall_rating = "🟢 STRONG STRATEGY"
    elif score >= 2:
        overall_rating = "🟡 DECENT STRATEGY"
    else:
        overall_rating = "🔴 WEAK STRATEGY"

    print(f"\nOverall Rating:         {overall_rating}")


def run_parameter_optimization():
    """Run basic parameter optimization for the strategy."""

    print("\n" + "=" * 70)
    print("PARAMETER OPTIMIZATION")
    print("=" * 70)

    # Parameter ranges to test
    param_combinations = [
        {"high_threshold_pct": 80, "low_threshold_pct": 20, "lookback_period": 50},
        {"high_threshold_pct": 85, "low_threshold_pct": 15, "lookback_period": 100},
        {"high_threshold_pct": 90, "low_threshold_pct": 10, "lookback_period": 150},
        {"high_threshold_pct": 75, "low_threshold_pct": 25, "lookback_period": 75},
        {"high_threshold_pct": 95, "low_threshold_pct": 5, "lookback_period": 200},
    ]

    best_sharpe = -999
    best_params = None
    results = []

    for i, params in enumerate(param_combinations):
        print(f"\nTesting parameter set {i+1}/{len(param_combinations)}: {params}")

        try:
            df_result, metrics = run_oi_strategy_backtest(
                timeframe="1d", start_date="2022-01-01", end_date="2024-12-31", **params
            )

            if metrics["sharpe_ratio"] > best_sharpe:
                best_sharpe = metrics["sharpe_ratio"]
                best_params = params.copy()

            results.append(
                {
                    "params": params,
                    "sharpe_ratio": metrics["sharpe_ratio"],
                    "total_return": metrics["total_return_pct"],
                    "max_drawdown": metrics["max_drawdown_pct"],
                }
            )

            print(
                f"  Sharpe: {metrics['sharpe_ratio']:.3f}, Return: {metrics['total_return_pct']:.1f}%, DD: {metrics['max_drawdown_pct']:.1f}%"
            )

        except Exception as e:
            print(f"  Error: {e}")

    print(f"\n🏆 BEST PARAMETERS:")
    print(f"Parameters: {best_params}")
    print(f"Best Sharpe Ratio: {best_sharpe:.3f}")

    return best_params, results


def main():
    """Main function to run the complete strategy analysis."""

    print("🚀 OI/Market Cap Strategy Analysis")

    # Run base strategy test
    df_result, metrics = run_oi_strategy_backtest(
        timeframe="1d", start_date="2022-01-01", end_date="2024-12-31"
    )

    if df_result is not None:
        # Print results
        print_strategy_results(metrics, df_result)

        # Create analysis plots
        save_dir = "backtest_results/oi_marketcap_strategy"
        create_strategy_analysis_plots(df_result, metrics, save_dir)

        # Save results
        results_path = f"{save_dir}/oi_marketcap_strategy_results.csv"
        df_result.to_csv(results_path, index=False)
        print(f"\nDetailed results saved to: {results_path}")

        # Run parameter optimization
        best_params, opt_results = run_parameter_optimization()

        # Test best parameters
        if best_params:
            print(f"\n" + "=" * 70)
            print("TESTING OPTIMIZED PARAMETERS")
            print("=" * 70)

            df_opt, metrics_opt = run_oi_strategy_backtest(
                timeframe="1d", start_date="2022-01-01", end_date="2024-12-31", **best_params
            )

            if df_opt is not None:
                print_strategy_results(metrics_opt, df_opt)

                # Save optimized results
                opt_save_dir = "backtest_results/oi_marketcap_strategy_optimized"
                create_strategy_analysis_plots(df_opt, metrics_opt, opt_save_dir)

                opt_results_path = f"{opt_save_dir}/oi_marketcap_optimized_results.csv"
                df_opt.to_csv(opt_results_path, index=False)
                print(f"\nOptimized results saved to: {opt_results_path}")

        print("\n✅ Strategy analysis completed!")

        return df_result, metrics

    else:
        print("❌ Strategy analysis failed!")
        return None, None


if __name__ == "__main__":
    result_df, result_metrics = main()
