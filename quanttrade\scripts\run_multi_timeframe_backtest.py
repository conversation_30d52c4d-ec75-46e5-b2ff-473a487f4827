"""
Run Backtests for Multiple Timeframes

This script runs backtests for a trading strategy on multiple timeframes
and compares the results.
"""

import argparse
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import os
import sys

from quanttrade.backtest.framework import BacktestFramework
from quanttrade.strategies import basic_pandas as basic, custom
from quanttrade.scripts.run_backtest import run_backtest


def run_multi_timeframe_backtest(
    data_file: str,
    strategy_name: str,
    start_date: str,
    end_date: str,
    initial_capital: float,
    commission: float,
    **strategy_params,
):
    """
    Run backtests on multiple timeframes and compare results.

    Args:
        data_file: Path to the CSV file containing price data
        strategy_name: Name of the strategy to use
        start_date: Start date for backtesting (YYYY-MM-DD)
        end_date: End date for backtesting (YYYY-MM-DD)
        initial_capital: Initial capital for backtesting
        commission: Commission rate for trades (as a decimal)
        strategy_params: Additional parameters for the strategy
    """
    # Use different timeframes based on strategy
    if strategy_name == "trend_demand_supply":
        # Skip 1m timeframe for trend_demand_supply strategy as it's too computationally intensive
        timeframes = ["4h", "1d"]
    else:
        timeframes = ["1m", "4h", "1d"]
    results = {}

    # Run backtests for each timeframe
    for timeframe in timeframes:
        print(f"\n{'='*50}")
        print(f"Running backtest for {timeframe} timeframe")
        print(f"{'='*50}")

        result = run_backtest(
            data_file=data_file,
            strategy_name=strategy_name,
            timeframe=timeframe,
            start_date=start_date,
            end_date=end_date,
            initial_capital=initial_capital,
            commission=commission,
            **strategy_params,
        )

        results[timeframe] = result

    # Compare results
    compare_results(results, strategy_name)


def compare_results(results, strategy_name):
    """
    Compare backtest results across different timeframes.

    Args:
        results: Dictionary of backtest results by timeframe
        strategy_name: Name of the strategy used
    """
    # Create output directory
    output_dir = f"backtest_results/{strategy_name}_comparison"
    os.makedirs(output_dir, exist_ok=True)

    # Prepare data for comparison
    comparison_data = []

    for timeframe, result in results.items():
        if result is not None and "equity_curve" in result:
            final_equity = result["equity_curve"].iloc[-1]
            sharpe_ratio = result["sharpe_ratio"]
            max_drawdown = result["max_drawdown"]
            num_trades = len(result["trades"])

            comparison_data.append(
                {
                    "Timeframe": timeframe,
                    "Final Equity": final_equity,
                    "Total Return (%)": (final_equity / 10000 - 1) * 100,
                    "Sharpe Ratio": sharpe_ratio,
                    "Max Drawdown (%)": max_drawdown,
                    "Number of Trades": num_trades,
                }
            )

    # Create comparison table
    if comparison_data:
        comparison_df = pd.DataFrame(comparison_data)
        comparison_df = comparison_df.set_index("Timeframe")

        # Save comparison table
        comparison_df.to_csv(f"{output_dir}/comparison_table.csv")

        # Print comparison table
        print("\nStrategy Comparison Across Timeframes:")
        print(comparison_df)

        # Plot comparison charts
        plt.figure(figsize=(12, 8))

        # Total Return comparison
        plt.subplot(2, 2, 1)
        comparison_df["Total Return (%)"].plot(kind="bar")
        plt.title("Total Return (%)")
        plt.ylabel("Return (%)")
        plt.grid(True, alpha=0.3)

        # Sharpe Ratio comparison
        plt.subplot(2, 2, 2)
        comparison_df["Sharpe Ratio"].plot(kind="bar")
        plt.title("Sharpe Ratio")
        plt.grid(True, alpha=0.3)

        # Max Drawdown comparison
        plt.subplot(2, 2, 3)
        comparison_df["Max Drawdown (%)"].plot(kind="bar")
        plt.title("Max Drawdown (%)")
        plt.ylabel("Drawdown (%)")
        plt.grid(True, alpha=0.3)

        # Number of Trades comparison
        plt.subplot(2, 2, 4)
        comparison_df["Number of Trades"].plot(kind="bar")
        plt.title("Number of Trades")
        plt.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f"{output_dir}/comparison_charts.png")
        plt.close()

        # Save summary to file
        with open(f"{output_dir}/comparison_summary.txt", "w") as f:
            f.write("Strategy Comparison Across Timeframes:\n\n")
            f.write(comparison_df.to_string())
            f.write("\n\nConclusion:\n")

            # Find best timeframe by return
            best_return_timeframe = comparison_df["Total Return (%)"].idxmax()
            best_return = comparison_df.loc[best_return_timeframe, "Total Return (%)"]

            # Find best timeframe by Sharpe ratio
            best_sharpe_timeframe = comparison_df["Sharpe Ratio"].idxmax()
            best_sharpe = comparison_df.loc[best_sharpe_timeframe, "Sharpe Ratio"]

            f.write(f"- Best timeframe by return: {best_return_timeframe} ({best_return:.2f}%)\n")
            f.write(
                f"- Best timeframe by Sharpe ratio: {best_sharpe_timeframe} ({best_sharpe:.2f})\n"
            )

            # Add more analysis as needed

        print(f"\nComparison results saved to {output_dir}/")
    else:
        print("No valid results to compare")


def main():
    """Main function to parse arguments and run multi-timeframe backtests."""
    parser = argparse.ArgumentParser(
        description="Run cryptocurrency trading strategy backtests on multiple timeframes"
    )

    parser.add_argument(
        "--data-file",
        type=str,
        default="quanttrade/data/datasets/btcusd_1_min_data.csv",
        help="Path to the CSV file containing price data",
    )

    parser.add_argument(
        "--strategy",
        type=str,
        default="template",
        choices=[
            "template",
            "custom",
        ],
        help="Trading strategy template to use",
    )

    parser.add_argument(
        "--start-date",
        type=str,
        default="2020-01-01",
        help="Start date for backtesting (YYYY-MM-DD)",
    )

    parser.add_argument(
        "--end-date", type=str, default="2025-01-27", help="End date for backtesting (YYYY-MM-DD)"
    )

    parser.add_argument(
        "--initial-capital", type=float, default=10000.0, help="Initial capital for backtesting"
    )

    parser.add_argument(
        "--trading-fee-rate",
        type=float,
        default=0.0005,
        help="Trading fee rate applied to both entry and exit (as a decimal)",
    )

    # Template strategy parameters (add your own parameters here)
    # parser.add_argument("--your-param", type=int, default=10, help="Your custom parameter")

    args = parser.parse_args()

    # Prepare strategy parameters (customize this for your strategy)
    strategy_params = {}
    # strategy_params = {"your_param": args.your_param}

    # Run multi-timeframe backtest
    run_multi_timeframe_backtest(
        data_file=args.data_file,
        strategy_name=args.strategy,
        start_date=args.start_date,
        end_date=args.end_date,
        initial_capital=args.initial_capital,
        commission=args.trading_fee_rate,  # Using trading_fee_rate for the commission parameter
        **strategy_params,
    )


if __name__ == "__main__":
    main()
