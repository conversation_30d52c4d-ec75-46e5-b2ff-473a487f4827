{"cells": [{"cell_type": "code", "execution_count": null, "id": "5cf5e828", "metadata": {}, "outputs": [], "source": ["import requests\n", "import time\n", "import pandas as pd\n", "\n", "def fetch_data(coin):\n", "    # --- CONFIGURATION ---\n", "    base_url = \"https://data-api.coindesk.com\"\n", "    endpoint = \"/futures/v1/historical/open-interest/hours\"\n", "    output_csv = f\"oi_20k_{coin}_data.csv\"\n", "\n", "    # Common query parameters\n", "    params = {\n", "        \"market\": \"binance\",\n", "        \"instrument\": coin,\n", "        \"groups\": \"ID,MAPPING,OHLC,OHLC_MESSAGE,MESSAGE\",\n", "        \"limit\": 2000,\n", "        \"aggregate\": 1,\n", "        \"fill\": \"true\",\n", "        \"apply_mapping\": \"true\"\n", "    }\n", "\n", "    # Starting timestamp (seconds)\n", "    to_ts = 1750165487\n", "\n", "    all_records = []\n", "\n", "    for i in range(10):\n", "        # Set the pagination timestamp\n", "        params[\"to_ts\"] = to_ts\n", "        response = requests.get(base_url + endpoint, params=params)\n", "        response.raise_for_status()\n", "        data = response.json().get(\"Data\", [])\n", "        if not data:\n", "            print(f\"No data returned on iteration {i}. Stopping.\")\n", "            break\n", "        \n", "        # Extend our list with this batch\n", "        all_records.extend(data)\n", "        \n", "        # Determine next to_ts: go just before the earliest timestamp in this batch\n", "        earliest_ts = min(item[\"TIMESTAMP\"] for item in data)\n", "        to_ts = earliest_ts - 1  # so next call returns earlier data\n", "        \n", "        print(f\"Iteration {i+1}: fetched {len(data)} points, next to_ts={to_ts}\")\n", "        \n", "        # Rate limit pause\n", "        time.sleep(0.2)\n", "\n", "    # Create DataFrame from all records\n", "    df = pd.DataFrame(all_records)\n", "\n", "    # Convert TIMESTAMP to datetime (UTC)\n", "    df[\"datetime\"] = pd.to_datetime(df[\"TIMESTAMP\"], unit=\"s\", utc=True)\n", "    df = df.set_index(\"datetime\").sort_index()\n", "\n", "    # Save to CSV and JSON\n", "    df.to_csv(output_csv)\n", "\n", "    print(f\"Saved combined data to {output_csv}\")\n"]}, {"cell_type": "code", "execution_count": 34, "id": "926a6729", "metadata": {}, "outputs": [], "source": ["# import time, requests, pandas as pd\n", "\n", "# API_KEY   = \"\"\n", "# BASE_URL  = \"https://rest.coincap.io\"\n", "# ENDPOINT  = \"/v3/assets/bitcoin/history\"\n", "# OUTPUT    = \"btc_20k_1h_marketcap.csv\"\n", "\n", "# limit     = 2000\n", "# interval  = \"h1\"\n", "\n", "# # Calculate start/end (ms)\n", "# now_ms    = int(time.time() * 1000)\n", "# start_ms  = now_ms - 20000 * 3600 * 1000  # 20k hours ago\n", "\n", "# records   = []\n", "# end_ts    = now_ms\n", "\n", "# for _ in range(10):\n", "#     params = {\n", "#         \"interval\": interval,\n", "#         \"start\":    start_ms,\n", "#         \"end\":      end_ts,\n", "#         \"limit\":    limit,\n", "#         \"apiKey\":   API_KEY\n", "#     }\n", "#     resp = requests.get(BASE_URL + ENDPOINT, params=params)\n", "#     resp.raise_for_status()\n", "#     batch = resp.json().get(\"data\", [])\n", "#     if not batch:\n", "#         break\n", "\n", "#     # collect and move the window\n", "#     records.extend(batch)\n", "#     earliest = min(item[\"time\"] for item in batch)\n", "#     end_ts   = earliest - 1\n", "#     time.sleep(0.2)\n", "\n", "# # Build DataFrame and save\n", "# df = pd.DataFrame(records)\n", "# df[\"datetime\"] = pd.to_datetime(df[\"time\"], unit=\"ms\", utc=True)\n", "# df.to_csv(OUTPUT)\n", "# print(f\"Saved {len(df)} hourly market‑cap points to {OUTPUT}\")\n"]}, {"cell_type": "markdown", "id": "1623fe39", "metadata": {}, "source": ["## BTC data cleaning"]}, {"cell_type": "code", "execution_count": 11, "id": "afacb707", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Missing datetime entries (present in one but not the other):\n", "[Timestamp('2024-05-19 10:00:00+0000', tz='UTC'), Timestamp('2024-05-21 20:00:00+0000', tz='UTC'), Timestamp('2024-08-26 21:00:00+0000', tz='UTC'), Timestamp('2024-08-26 22:00:00+0000', tz='UTC'), Timestamp('2024-08-26 23:00:00+0000', tz='UTC'), Timestamp('2024-08-27 16:00:00+0000', tz='UTC'), Timestamp('2024-09-01 18:00:00+0000', tz='UTC'), Timestamp('2024-09-02 13:00:00+0000', tz='UTC'), Timestamp('2024-09-03 03:00:00+0000', tz='UTC'), Timestamp('2024-09-03 07:00:00+0000', tz='UTC'), Timestamp('2024-09-04 19:00:00+0000', tz='UTC'), Timestamp('2024-09-04 22:00:00+0000', tz='UTC'), Timestamp('2024-09-05 01:00:00+0000', tz='UTC'), Timestamp('2024-09-05 02:00:00+0000', tz='UTC'), Timestamp('2024-09-14 05:00:00+0000', tz='UTC'), Timestamp('2024-09-14 22:00:00+0000', tz='UTC'), Timestamp('2024-09-16 08:00:00+0000', tz='UTC'), Timestamp('2024-09-18 04:00:00+0000', tz='UTC'), Timestamp('2024-09-18 06:00:00+0000', tz='UTC'), Timestamp('2024-09-19 06:00:00+0000', tz='UTC'), Timestamp('2024-09-19 14:00:00+0000', tz='UTC'), Timestamp('2024-09-20 14:00:00+0000', tz='UTC'), Timestamp('2024-09-23 21:00:00+0000', tz='UTC'), Timestamp('2024-09-25 18:00:00+0000', tz='UTC'), Timestamp('2025-01-16 20:00:00+0000', tz='UTC'), Timestamp('2025-01-16 21:00:00+0000', tz='UTC')]\n", "Saved merged data with price-based ratio (19974 rows) to merged_with_indicators.csv\n"]}], "source": ["import pandas as pd\n", "\n", "# 1) Process Market-Cap Data (as before)\n", "mc = pd.read_csv(\"btc_20k_1h_marketcap.csv\", parse_dates=[\"datetime\"])\n", "mc[\"marketCapUsd\"] = mc[\"priceUsd\"] * mc[\"circulatingSupply\"]\n", "mc = mc[[\"datetime\", \"priceUsd\", \"circulatingSupply\", \"marketCapUsd\"]]\n", "\n", "# 2) Process Open Interest Data (value + count)\n", "oi = pd.read_csv(\"oi_20k_data.csv\", parse_dates=[\"datetime\"])\n", "oi = oi[[\"datetime\", \"CLOSE_QUOTE\", \"CLOSE_SETTLEMENT\"]].rename(\n", "    columns={\n", "        \"CLOSE_QUOTE\":      \"open_interest_value\",\n", "        \"CLOSE_SETTLEMENT\": \"open_interest_count\"\n", "    }\n", ")\n", "\n", "# 3) Merge & report mismatches\n", "merged = oi.merge(mc, on=\"datetime\", how=\"outer\", indicator=True)\n", "missing = merged.loc[merged[\"_merge\"] != \"both\", \"datetime\"]\n", "if not missing.empty:\n", "    print(\"Missing datetime entries (present in one but not the other):\")\n", "    print(missing.tolist())\n", "else:\n", "    print(\"All timestamps align.\")\n", "\n", "# Keep only aligned rows\n", "merged = merged[merged[\"_merge\"] == \"both\"].drop(columns=[\"_merge\"])\n", "\n", "# 4) Compute Indicators\n", "merged[\"oi_mc_ratio\"]    = merged[\"open_interest_value\"] / merged[\"marketCapUsd\"]\n", "merged[\"oi_price_ratio\"] = merged[\"open_interest_value\"] / merged[\"priceUsd\"]\n", "merged[\"price_pct_change\"]= merged[\"priceUsd\"].pct_change()\n", "\n", "# 5) Save the final merged dataset\n", "output_file = \"merged_with_indicators.csv\"\n", "merged.to_csv(output_file, index=False)\n", "print(f\"Saved merged data with price-based ratio ({len(merged)} rows) to {output_file}\")\n"]}, {"cell_type": "markdown", "id": "5e1aa2d4", "metadata": {}, "source": ["## BTC backtesting"]}, {"cell_type": "code", "execution_count": null, "id": "497a2891", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Best thresholds:\n", "  Lower = 0.003554, Upper = 0.004290\n", "\n", "Training | Sharpe: -4.805, Total Return: -98.22%\n", "Validation | Sharpe: nan, Total Return: 0.00%\n", "Testing | Sharpe: nan, Total Return: 0.00%\n"]}, {"ename": "PermissionError", "evalue": "[<PERSON>rr<PERSON> 13] Permission denied: 'threshold_sweep_train.csv'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mPermissionError\u001b[0m                           <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[39], line 63\u001b[0m\n\u001b[0;32m     60\u001b[0m compute_metrics(test_df,  \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mTesting\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m     62\u001b[0m \u001b[38;5;66;03m# 6) Save results\u001b[39;00m\n\u001b[1;32m---> 63\u001b[0m results_df\u001b[38;5;241m.\u001b[39mto_csv(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mthreshold_sweep_train.csv\u001b[39m\u001b[38;5;124m\"\u001b[39m, index\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m)\n\u001b[0;32m     64\u001b[0m full_df\u001b[38;5;241m.\u001b[39mto_csv(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mbacktest_three_splits.csv\u001b[39m\u001b[38;5;124m\"\u001b[39m, index\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m)\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pandas\\util\\_decorators.py:333\u001b[0m, in \u001b[0;36mdeprecate_nonkeyword_arguments.<locals>.decorate.<locals>.wrapper\u001b[1;34m(*args, **kwargs)\u001b[0m\n\u001b[0;32m    327\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(args) \u001b[38;5;241m>\u001b[39m num_allow_args:\n\u001b[0;32m    328\u001b[0m     warnings\u001b[38;5;241m.\u001b[39mwarn(\n\u001b[0;32m    329\u001b[0m         msg\u001b[38;5;241m.\u001b[39mformat(arguments\u001b[38;5;241m=\u001b[39m_format_argument_list(allow_args)),\n\u001b[0;32m    330\u001b[0m         \u001b[38;5;167;01mFutureWarning\u001b[39;00m,\n\u001b[0;32m    331\u001b[0m         stacklevel\u001b[38;5;241m=\u001b[39mfind_stack_level(),\n\u001b[0;32m    332\u001b[0m     )\n\u001b[1;32m--> 333\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m func(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pandas\\core\\generic.py:3967\u001b[0m, in \u001b[0;36mNDFrame.to_csv\u001b[1;34m(self, path_or_buf, sep, na_rep, float_format, columns, header, index, index_label, mode, encoding, compression, quoting, quotechar, lineterminator, chunksize, date_format, doublequote, escapechar, decimal, errors, storage_options)\u001b[0m\n\u001b[0;32m   3956\u001b[0m df \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(\u001b[38;5;28mself\u001b[39m, ABCDataFrame) \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mto_frame()\n\u001b[0;32m   3958\u001b[0m formatter \u001b[38;5;241m=\u001b[39m DataFrameFormatter(\n\u001b[0;32m   3959\u001b[0m     frame\u001b[38;5;241m=\u001b[39mdf,\n\u001b[0;32m   3960\u001b[0m     header\u001b[38;5;241m=\u001b[39mheader,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   3964\u001b[0m     decimal\u001b[38;5;241m=\u001b[39mdecimal,\n\u001b[0;32m   3965\u001b[0m )\n\u001b[1;32m-> 3967\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m DataFrameRenderer(formatter)\u001b[38;5;241m.\u001b[39mto_csv(\n\u001b[0;32m   3968\u001b[0m     path_or_buf,\n\u001b[0;32m   3969\u001b[0m     lineterminator\u001b[38;5;241m=\u001b[39mlineterminator,\n\u001b[0;32m   3970\u001b[0m     sep\u001b[38;5;241m=\u001b[39msep,\n\u001b[0;32m   3971\u001b[0m     encoding\u001b[38;5;241m=\u001b[39mencoding,\n\u001b[0;32m   3972\u001b[0m     errors\u001b[38;5;241m=\u001b[39merrors,\n\u001b[0;32m   3973\u001b[0m     compression\u001b[38;5;241m=\u001b[39mcompression,\n\u001b[0;32m   3974\u001b[0m     quoting\u001b[38;5;241m=\u001b[39mquoting,\n\u001b[0;32m   3975\u001b[0m     columns\u001b[38;5;241m=\u001b[39mcolumns,\n\u001b[0;32m   3976\u001b[0m     index_label\u001b[38;5;241m=\u001b[39mindex_label,\n\u001b[0;32m   3977\u001b[0m     mode\u001b[38;5;241m=\u001b[39mmode,\n\u001b[0;32m   3978\u001b[0m     chunksize\u001b[38;5;241m=\u001b[39mchunksize,\n\u001b[0;32m   3979\u001b[0m     quotechar\u001b[38;5;241m=\u001b[39mquotechar,\n\u001b[0;32m   3980\u001b[0m     date_format\u001b[38;5;241m=\u001b[39mdate_format,\n\u001b[0;32m   3981\u001b[0m     doublequote\u001b[38;5;241m=\u001b[39mdoublequote,\n\u001b[0;32m   3982\u001b[0m     escapechar\u001b[38;5;241m=\u001b[39mescapechar,\n\u001b[0;32m   3983\u001b[0m     storage_options\u001b[38;5;241m=\u001b[39mstorage_options,\n\u001b[0;32m   3984\u001b[0m )\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pandas\\io\\formats\\format.py:1014\u001b[0m, in \u001b[0;36mDataFrameRenderer.to_csv\u001b[1;34m(self, path_or_buf, encoding, sep, columns, index_label, mode, compression, quoting, quotechar, lineterminator, chunksize, date_format, doublequote, escapechar, errors, storage_options)\u001b[0m\n\u001b[0;32m    993\u001b[0m     created_buffer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mFalse\u001b[39;00m\n\u001b[0;32m    995\u001b[0m csv_formatter \u001b[38;5;241m=\u001b[39m CSVFormatter(\n\u001b[0;32m    996\u001b[0m     path_or_buf\u001b[38;5;241m=\u001b[39mpath_or_buf,\n\u001b[0;32m    997\u001b[0m     lineterminator\u001b[38;5;241m=\u001b[39mlineterminator,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   1012\u001b[0m     formatter\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mfmt,\n\u001b[0;32m   1013\u001b[0m )\n\u001b[1;32m-> 1014\u001b[0m csv_formatter\u001b[38;5;241m.\u001b[39msave()\n\u001b[0;32m   1016\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m created_buffer:\n\u001b[0;32m   1017\u001b[0m     \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(path_or_buf, StringIO)\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py:251\u001b[0m, in \u001b[0;36mCSVFormatter.save\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    247\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m    248\u001b[0m \u001b[38;5;124;03m<PERSON>reate the writer & save.\u001b[39;00m\n\u001b[0;32m    249\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m    250\u001b[0m \u001b[38;5;66;03m# apply compression and byte/text conversion\u001b[39;00m\n\u001b[1;32m--> 251\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m get_handle(\n\u001b[0;32m    252\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mfilepath_or_buffer,\n\u001b[0;32m    253\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmode,\n\u001b[0;32m    254\u001b[0m     encoding\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mencoding,\n\u001b[0;32m    255\u001b[0m     errors\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39merrors,\n\u001b[0;32m    256\u001b[0m     compression\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcompression,\n\u001b[0;32m    257\u001b[0m     storage_options\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstorage_options,\n\u001b[0;32m    258\u001b[0m ) \u001b[38;5;28;01mas\u001b[39;00m handles:\n\u001b[0;32m    259\u001b[0m     \u001b[38;5;66;03m# Note: self.encoding is irrelevant here\u001b[39;00m\n\u001b[0;32m    260\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mwriter \u001b[38;5;241m=\u001b[39m csvlib\u001b[38;5;241m.\u001b[39mwriter(\n\u001b[0;32m    261\u001b[0m         handles\u001b[38;5;241m.\u001b[39mhandle,\n\u001b[0;32m    262\u001b[0m         lineterminator\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mlineterminator,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    267\u001b[0m         quotechar\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mquotechar,\n\u001b[0;32m    268\u001b[0m     )\n\u001b[0;32m    270\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_save()\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pandas\\io\\common.py:873\u001b[0m, in \u001b[0;36mget_handle\u001b[1;34m(path_or_buf, mode, encoding, compression, memory_map, is_text, errors, storage_options)\u001b[0m\n\u001b[0;32m    868\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(handle, \u001b[38;5;28mstr\u001b[39m):\n\u001b[0;32m    869\u001b[0m     \u001b[38;5;66;03m# Check whether the filename is to be opened in binary mode.\u001b[39;00m\n\u001b[0;32m    870\u001b[0m     \u001b[38;5;66;03m# Binary mode does not support 'encoding' and 'newline'.\u001b[39;00m\n\u001b[0;32m    871\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m ioargs\u001b[38;5;241m.\u001b[39mencoding \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m ioargs\u001b[38;5;241m.\u001b[39mmode:\n\u001b[0;32m    872\u001b[0m         \u001b[38;5;66;03m# Encoding\u001b[39;00m\n\u001b[1;32m--> 873\u001b[0m         handle \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mopen\u001b[39m(\n\u001b[0;32m    874\u001b[0m             handle,\n\u001b[0;32m    875\u001b[0m             ioargs\u001b[38;5;241m.\u001b[39mmode,\n\u001b[0;32m    876\u001b[0m             encoding\u001b[38;5;241m=\u001b[39mioargs\u001b[38;5;241m.\u001b[39mencoding,\n\u001b[0;32m    877\u001b[0m             errors\u001b[38;5;241m=\u001b[39merrors,\n\u001b[0;32m    878\u001b[0m             newline\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m    879\u001b[0m         )\n\u001b[0;32m    880\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m    881\u001b[0m         \u001b[38;5;66;03m# Binary mode\u001b[39;00m\n\u001b[0;32m    882\u001b[0m         handle \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mopen\u001b[39m(handle, ioargs\u001b[38;5;241m.\u001b[39mmode)\n", "\u001b[1;31mPermissionError\u001b[0m: [Errno 13] Permission denied: 'threshold_sweep_train.csv'"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "# 1) Load merged data\n", "df = pd.read_csv(\"merged_with_indicators.csv\", parse_dates=[\"datetime\"])\n", "df = df.sort_values(\"datetime\").reset_index(drop=True)\n", "\n", "# 2) Define splits: training, validation, testing\n", "n_valid = 2\n", "n_test  = 2\n", "train_df = df.iloc[:-(n_valid + n_test)].copy()\n", "valid_df = df.iloc[-(n_valid + n_test):-n_test].copy()\n", "test_df  = df.iloc[-n_test:].copy()\n", "full_df  = df.copy()\n", "\n", "# 3) Grid-search on training for OI/MC ratio thresholds\n", "ratios = train_df[\"oi_mc_ratio\"].dropna()\n", "lower_min, lower_max = ratios.quantile([0.01, 0.5])\n", "upper_min, upper_max = ratios.quantile([0.5, 0.99])\n", "lower_bounds = np.linspace(lower_min, lower_max, 20)\n", "upper_bounds = np.linspace(upper_min, upper_max, 20)\n", "\n", "results = []\n", "for low in lower_bounds:\n", "    for high in upper_bounds:\n", "        if low >= high:\n", "            continue\n", "        train_df[\"position\"] = np.where(\n", "            train_df[\"oi_mc_ratio\"] < low, -1,\n", "            np.where(train_df[\"oi_mc_ratio\"] > high, 1, 0)\n", "        )\n", "        strat_ret = train_df[\"position\"].shift(1) * train_df[\"price_pct_change\"]\n", "        sharpe = strat_ret.mean() / strat_ret.std() * np.sqrt(365 * 24)\n", "        results.append({\"lower\": low, \"upper\": high, \"sharpe_train\": sharpe})\n", "\n", "results_df = pd.DataFrame(results)\n", "best = results_df.loc[results_df[\"sharpe_train\"].idxmax()]\n", "lower_best, upper_best = best[\"lower\"], best[\"upper\"]\n", "\n", "# 4) Apply best thresholds to all splits\n", "for subset in [train_df, valid_df, test_df, full_df]:\n", "    subset[\"position\"] = np.where(\n", "        subset[\"oi_mc_ratio\"] < lower_best, -1,\n", "        np.where(subset[\"oi_mc_ratio\"] > upper_best, 1, 0)\n", "    )\n", "    subset[\"strat_ret\"] = subset[\"position\"].shift(1) * subset[\"price_pct_change\"]\n", "    subset[\"equity\"]    = (1 + subset[\"strat_ret\"].fillna(0)).cumprod()\n", "\n", "# 5) Compute metrics for each split\n", "def compute_metrics(df, name):\n", "    total_ret = df[\"equity\"].iloc[-1] - 1\n", "    sharpe    = df[\"strat_ret\"].mean() / df[\"strat_ret\"].std() * np.sqrt(365 * 24)\n", "    print(f\"{name} | Sharpe: {sharpe:.3f}, Total Return: {total_ret:.2%}\")\n", "\n", "print(\"Best thresholds:\")\n", "print(f\"  Lower = {lower_best:.6f}, Upper = {upper_best:.6f}\\n\")\n", "\n", "compute_metrics(train_df, \"Training\")\n", "compute_metrics(valid_df, \"Validation\")\n", "compute_metrics(test_df,  \"Testing\")\n", "\n", "# 6) Save results\n", "results_df.to_csv(\"threshold_sweep_train.csv\", index=False)\n", "full_df.to_csv(\"backtest_three_splits.csv\", index=False)\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "id": "04a6f0bc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Backtest on last 5000 candles:\n", "Total Return: 7.45%\n", "Sharpe Ratio: 1.759\n", "Saved backtest of recent 5000 candles to backtest_last5000.csv\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "# Load merged data\n", "df = pd.read_csv(\"merged_with_indicators.csv\", parse_dates=[\"datetime\"])\n", "df = df.sort_values(\"datetime\").reset_index(drop=True)\n", "\n", "# Only use the most recent 5000 candles\n", "df_recent = df.tail(5000).copy().reset_index(drop=True)\n", "\n", "# Define your thresholds (replace with your optimized values)\n", "lower_threshold = 0.003392\n", "upper_threshold = 0.006045\n", "\n", "# Generate positions: -1 = short, +1 = long, 0 = flat\n", "df_recent[\"position\"] = np.where(\n", "    df_recent[\"oi_mc_ratio\"] < lower_threshold, -1,\n", "    np.where(df_recent[\"oi_mc_ratio\"] > upper_threshold, 1, 0)\n", ")\n", "\n", "# Compute strategy returns and equity\n", "df_recent[\"strat_ret\"] = df_recent[\"position\"].shift(1) * df_recent[\"price_pct_change\"]\n", "df_recent[\"equity\"]    = (1 + df_recent[\"strat_ret\"].fillna(0)).cumprod()\n", "\n", "# Calculate performance metrics\n", "total_return = df_recent[\"equity\"].iloc[-1] - 1\n", "sharpe       = df_recent[\"strat_ret\"].mean() / df_recent[\"strat_ret\"].std() * np.sqrt(365 * 24)\n", "\n", "print(f\"Backtest on last 5000 candles:\")\n", "print(f\"Total Return: {total_return:.2%}\")\n", "print(f\"<PERSON>: {sharpe:.3f}\")\n", "\n", "# Save results\n", "output_file = \"backtest_last5000.csv\"\n", "df_recent.to_csv(output_file, index=False)\n", "print(f\"Saved backtest of recent 5000 candles to {output_file}\")\n"]}, {"cell_type": "code", "execution_count": 4, "id": "f09a5873", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>upper</th>\n", "      <th>0.004380</th>\n", "      <th>0.004473</th>\n", "      <th>0.004565</th>\n", "      <th>0.004658</th>\n", "      <th>0.004750</th>\n", "      <th>0.004843</th>\n", "      <th>0.004935</th>\n", "      <th>0.005028</th>\n", "      <th>0.005120</th>\n", "      <th>0.005213</th>\n", "      <th>0.005305</th>\n", "      <th>0.005398</th>\n", "      <th>0.005490</th>\n", "      <th>0.005583</th>\n", "      <th>0.005675</th>\n", "      <th>0.005768</th>\n", "      <th>0.005860</th>\n", "      <th>0.005953</th>\n", "      <th>0.006045</th>\n", "      <th>0.006138</th>\n", "    </tr>\n", "    <tr>\n", "      <th>lower</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0.003392</th>\n", "      <td>3.471460</td>\n", "      <td>3.335070</td>\n", "      <td>3.201312</td>\n", "      <td>2.971854</td>\n", "      <td>2.811158</td>\n", "      <td>2.477010</td>\n", "      <td>1.979099</td>\n", "      <td>2.084045</td>\n", "      <td>2.444382</td>\n", "      <td>3.204094</td>\n", "      <td>3.652820</td>\n", "      <td>4.117634</td>\n", "      <td>4.246279</td>\n", "      <td>4.501882</td>\n", "      <td>4.229716</td>\n", "      <td>4.546329</td>\n", "      <td>4.418837</td>\n", "      <td>4.278275</td>\n", "      <td>5.056115</td>\n", "      <td>4.465395</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.003444</th>\n", "      <td>3.356454</td>\n", "      <td>3.217577</td>\n", "      <td>3.076798</td>\n", "      <td>2.847561</td>\n", "      <td>2.686759</td>\n", "      <td>2.359250</td>\n", "      <td>1.866005</td>\n", "      <td>1.962569</td>\n", "      <td>2.301861</td>\n", "      <td>3.015165</td>\n", "      <td>3.409682</td>\n", "      <td>3.817598</td>\n", "      <td>3.907485</td>\n", "      <td>4.094496</td>\n", "      <td>3.767662</td>\n", "      <td>3.996238</td>\n", "      <td>3.820562</td>\n", "      <td>3.615504</td>\n", "      <td>4.196094</td>\n", "      <td>3.565650</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.003496</th>\n", "      <td>3.369976</td>\n", "      <td>3.232979</td>\n", "      <td>3.092480</td>\n", "      <td>2.869141</td>\n", "      <td>2.712713</td>\n", "      <td>2.397177</td>\n", "      <td>1.922525</td>\n", "      <td>2.013699</td>\n", "      <td>2.334176</td>\n", "      <td>3.002872</td>\n", "      <td>3.355516</td>\n", "      <td>3.716485</td>\n", "      <td>3.780387</td>\n", "      <td>3.920027</td>\n", "      <td>3.579203</td>\n", "      <td>3.749979</td>\n", "      <td>3.561990</td>\n", "      <td>3.343230</td>\n", "      <td>3.796147</td>\n", "      <td>3.209579</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.003548</th>\n", "      <td>3.331891</td>\n", "      <td>3.194848</td>\n", "      <td>3.052465</td>\n", "      <td>2.831314</td>\n", "      <td>2.676424</td>\n", "      <td>2.366960</td>\n", "      <td>1.900004</td>\n", "      <td>1.987016</td>\n", "      <td>2.295468</td>\n", "      <td>2.937345</td>\n", "      <td>3.264196</td>\n", "      <td>3.598470</td>\n", "      <td>3.647758</td>\n", "      <td>3.762414</td>\n", "      <td>3.414638</td>\n", "      <td>3.558480</td>\n", "      <td>3.366155</td>\n", "      <td>3.143444</td>\n", "      <td>3.543708</td>\n", "      <td>2.981330</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.003600</th>\n", "      <td>3.180378</td>\n", "      <td>3.040179</td>\n", "      <td>2.890895</td>\n", "      <td>2.667937</td>\n", "      <td>2.511461</td>\n", "      <td>2.204067</td>\n", "      <td>1.734921</td>\n", "      <td>1.816221</td>\n", "      <td>2.112592</td>\n", "      <td>2.730371</td>\n", "      <td>3.029895</td>\n", "      <td>3.339268</td>\n", "      <td>3.372948</td>\n", "      <td>3.463249</td>\n", "      <td>3.100244</td>\n", "      <td>3.221073</td>\n", "      <td>3.020101</td>\n", "      <td>2.788078</td>\n", "      <td>3.152014</td>\n", "      <td>2.596537</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.003652</th>\n", "      <td>3.130656</td>\n", "      <td>2.990548</td>\n", "      <td>2.839615</td>\n", "      <td>2.619118</td>\n", "      <td>2.464388</td>\n", "      <td>2.163315</td>\n", "      <td>1.702375</td>\n", "      <td>1.779693</td>\n", "      <td>2.064459</td>\n", "      <td>2.656756</td>\n", "      <td>2.933725</td>\n", "      <td>3.220336</td>\n", "      <td>3.242987</td>\n", "      <td>3.314842</td>\n", "      <td>2.952164</td>\n", "      <td>3.054575</td>\n", "      <td>2.854816</td>\n", "      <td>2.625578</td>\n", "      <td>2.953552</td>\n", "      <td>2.424950</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.003704</th>\n", "      <td>3.007749</td>\n", "      <td>2.865980</td>\n", "      <td>2.710516</td>\n", "      <td>2.490350</td>\n", "      <td>2.335700</td>\n", "      <td>2.038966</td>\n", "      <td>1.581076</td>\n", "      <td>1.653431</td>\n", "      <td>1.926203</td>\n", "      <td>2.493769</td>\n", "      <td>2.746857</td>\n", "      <td>3.011203</td>\n", "      <td>3.022120</td>\n", "      <td>3.075525</td>\n", "      <td>2.708830</td>\n", "      <td>2.794341</td>\n", "      <td>2.593373</td>\n", "      <td>2.363842</td>\n", "      <td>2.662727</td>\n", "      <td>2.152046</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.003756</th>\n", "      <td>3.253457</td>\n", "      <td>3.118540</td>\n", "      <td>2.973916</td>\n", "      <td>2.762662</td>\n", "      <td>2.614903</td>\n", "      <td>2.327204</td>\n", "      <td>1.890631</td>\n", "      <td>1.965979</td>\n", "      <td>2.238486</td>\n", "      <td>2.800575</td>\n", "      <td>3.059509</td>\n", "      <td>3.324268</td>\n", "      <td>3.341257</td>\n", "      <td>3.401148</td>\n", "      <td>3.059121</td>\n", "      <td>3.147460</td>\n", "      <td>2.961122</td>\n", "      <td>2.749207</td>\n", "      <td>3.038994</td>\n", "      <td>2.561536</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.003808</th>\n", "      <td>3.496552</td>\n", "      <td>3.368298</td>\n", "      <td>3.233781</td>\n", "      <td>3.031551</td>\n", "      <td>2.890701</td>\n", "      <td>2.612845</td>\n", "      <td>2.197886</td>\n", "      <td>2.275156</td>\n", "      <td>2.545013</td>\n", "      <td>3.096964</td>\n", "      <td>3.357063</td>\n", "      <td>3.618156</td>\n", "      <td>3.638867</td>\n", "      <td>3.701693</td>\n", "      <td>3.382069</td>\n", "      <td>3.470101</td>\n", "      <td>3.296955</td>\n", "      <td>3.100889</td>\n", "      <td>3.377576</td>\n", "      <td>2.932603</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.003860</th>\n", "      <td>3.593492</td>\n", "      <td>3.468442</td>\n", "      <td>3.337863</td>\n", "      <td>3.141014</td>\n", "      <td>3.004175</td>\n", "      <td>2.733829</td>\n", "      <td>2.332325</td>\n", "      <td>2.408481</td>\n", "      <td>2.671687</td>\n", "      <td>3.207644</td>\n", "      <td>3.458870</td>\n", "      <td>3.709419</td>\n", "      <td>3.727925</td>\n", "      <td>3.785863</td>\n", "      <td>3.477331</td>\n", "      <td>3.559524</td>\n", "      <td>3.393194</td>\n", "      <td>3.205536</td>\n", "      <td>3.466056</td>\n", "      <td>3.043844</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.003912</th>\n", "      <td>3.446482</td>\n", "      <td>3.320347</td>\n", "      <td>3.185242</td>\n", "      <td>2.990512</td>\n", "      <td>2.854986</td>\n", "      <td>2.592324</td>\n", "      <td>2.198125</td>\n", "      <td>2.267652</td>\n", "      <td>2.514229</td>\n", "      <td>3.016404</td>\n", "      <td>3.238678</td>\n", "      <td>3.462903</td>\n", "      <td>3.469479</td>\n", "      <td>3.508945</td>\n", "      <td>3.201887</td>\n", "      <td>3.267938</td>\n", "      <td>3.103702</td>\n", "      <td>2.919358</td>\n", "      <td>3.151763</td>\n", "      <td>2.752041</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.003964</th>\n", "      <td>3.444768</td>\n", "      <td>3.320406</td>\n", "      <td>3.186227</td>\n", "      <td>2.995983</td>\n", "      <td>2.863672</td>\n", "      <td>2.609014</td>\n", "      <td>2.226443</td>\n", "      <td>2.292651</td>\n", "      <td>2.528556</td>\n", "      <td>3.007877</td>\n", "      <td>3.214558</td>\n", "      <td>3.423438</td>\n", "      <td>3.425219</td>\n", "      <td>3.456482</td>\n", "      <td>3.158365</td>\n", "      <td>3.216525</td>\n", "      <td>3.058028</td>\n", "      <td>2.880791</td>\n", "      <td>3.095653</td>\n", "      <td>2.717419</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.004016</th>\n", "      <td>3.501379</td>\n", "      <td>3.379922</td>\n", "      <td>3.248631</td>\n", "      <td>3.063958</td>\n", "      <td>2.935695</td>\n", "      <td>2.689591</td>\n", "      <td>2.320641</td>\n", "      <td>2.384426</td>\n", "      <td>2.611079</td>\n", "      <td>3.070088</td>\n", "      <td>3.264923</td>\n", "      <td>3.461502</td>\n", "      <td>3.460663</td>\n", "      <td>3.486809</td>\n", "      <td>3.200189</td>\n", "      <td>3.252868</td>\n", "      <td>3.101294</td>\n", "      <td>2.932346</td>\n", "      <td>3.132675</td>\n", "      <td>2.775669</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.004068</th>\n", "      <td>3.275131</td>\n", "      <td>3.151205</td>\n", "      <td>3.014186</td>\n", "      <td>2.828878</td>\n", "      <td>2.699918</td>\n", "      <td>2.456900</td>\n", "      <td>2.087991</td>\n", "      <td>2.146805</td>\n", "      <td>2.362848</td>\n", "      <td>2.801824</td>\n", "      <td>2.978415</td>\n", "      <td>3.159867</td>\n", "      <td>3.151684</td>\n", "      <td>3.167221</td>\n", "      <td>2.879010</td>\n", "      <td>2.923319</td>\n", "      <td>2.771612</td>\n", "      <td>2.602913</td>\n", "      <td>2.790598</td>\n", "      <td>2.441862</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.004120</th>\n", "      <td>3.020942</td>\n", "      <td>2.894165</td>\n", "      <td>2.751033</td>\n", "      <td>2.564426</td>\n", "      <td>2.434296</td>\n", "      <td>2.193353</td>\n", "      <td>1.822903</td>\n", "      <td>1.877069</td>\n", "      <td>2.083688</td>\n", "      <td>2.505258</td>\n", "      <td>2.665422</td>\n", "      <td>2.833609</td>\n", "      <td>2.818711</td>\n", "      <td>2.824733</td>\n", "      <td>2.534046</td>\n", "      <td>2.571069</td>\n", "      <td>2.418677</td>\n", "      <td>2.249538</td>\n", "      <td>2.426835</td>\n", "      <td>2.083980</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.004172</th>\n", "      <td>2.544119</td>\n", "      <td>2.411750</td>\n", "      <td>2.257525</td>\n", "      <td>2.067351</td>\n", "      <td>1.934256</td>\n", "      <td>1.694733</td>\n", "      <td>1.318432</td>\n", "      <td>1.365404</td>\n", "      <td>1.558485</td>\n", "      <td>1.955860</td>\n", "      <td>2.091610</td>\n", "      <td>2.240781</td>\n", "      <td>2.215507</td>\n", "      <td>2.207188</td>\n", "      <td>1.910134</td>\n", "      <td>1.936591</td>\n", "      <td>1.781720</td>\n", "      <td>1.610246</td>\n", "      <td>1.773901</td>\n", "      <td>1.436257</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.004224</th>\n", "      <td>2.329228</td>\n", "      <td>2.195088</td>\n", "      <td>2.036857</td>\n", "      <td>1.846250</td>\n", "      <td>1.712691</td>\n", "      <td>1.475283</td>\n", "      <td>1.099050</td>\n", "      <td>1.142714</td>\n", "      <td>1.328789</td>\n", "      <td>1.713122</td>\n", "      <td>1.837532</td>\n", "      <td>1.977492</td>\n", "      <td>1.948035</td>\n", "      <td>1.933754</td>\n", "      <td>1.636683</td>\n", "      <td>1.658556</td>\n", "      <td>1.504153</td>\n", "      <td>1.333442</td>\n", "      <td>1.489952</td>\n", "      <td>1.157916</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.004276</th>\n", "      <td>2.313795</td>\n", "      <td>2.181016</td>\n", "      <td>2.024175</td>\n", "      <td>1.836182</td>\n", "      <td>1.704513</td>\n", "      <td>1.471009</td>\n", "      <td>1.100961</td>\n", "      <td>1.143611</td>\n", "      <td>1.325783</td>\n", "      <td>1.701761</td>\n", "      <td>1.822128</td>\n", "      <td>1.957895</td>\n", "      <td>1.928169</td>\n", "      <td>1.913006</td>\n", "      <td>1.621851</td>\n", "      <td>1.642507</td>\n", "      <td>1.491506</td>\n", "      <td>1.324741</td>\n", "      <td>1.476659</td>\n", "      <td>1.153199</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.004328</th>\n", "      <td>2.034383</td>\n", "      <td>1.899094</td>\n", "      <td>1.737088</td>\n", "      <td>1.547840</td>\n", "      <td>1.415085</td>\n", "      <td>1.182968</td>\n", "      <td>0.811227</td>\n", "      <td>0.850277</td>\n", "      <td>1.025363</td>\n", "      <td>1.388554</td>\n", "      <td>1.497066</td>\n", "      <td>1.623542</td>\n", "      <td>1.589225</td>\n", "      <td>1.567688</td>\n", "      <td>1.275086</td>\n", "      <td>1.291030</td>\n", "      <td>1.139727</td>\n", "      <td>0.972841</td>\n", "      <td>1.118138</td>\n", "      <td>0.798678</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0.004380</th>\n", "      <td>NaN</td>\n", "      <td>1.471586</td>\n", "      <td>1.301448</td>\n", "      <td>1.109296</td>\n", "      <td>0.974184</td>\n", "      <td>0.742492</td>\n", "      <td>0.365780</td>\n", "      <td>0.400025</td>\n", "      <td>0.566433</td>\n", "      <td>0.914519</td>\n", "      <td>1.007619</td>\n", "      <td>1.122463</td>\n", "      <td>1.081721</td>\n", "      <td>1.051488</td>\n", "      <td>0.754610</td>\n", "      <td>0.764378</td>\n", "      <td>0.611386</td>\n", "      <td>0.442868</td>\n", "      <td>0.580596</td>\n", "      <td>0.263577</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["upper     0.004380  0.004473  0.004565  0.004658  0.004750  0.004843  \\\n", "lower                                                                  \n", "0.003392  3.471460  3.335070  3.201312  2.971854  2.811158  2.477010   \n", "0.003444  3.356454  3.217577  3.076798  2.847561  2.686759  2.359250   \n", "0.003496  3.369976  3.232979  3.092480  2.869141  2.712713  2.397177   \n", "0.003548  3.331891  3.194848  3.052465  2.831314  2.676424  2.366960   \n", "0.003600  3.180378  3.040179  2.890895  2.667937  2.511461  2.204067   \n", "0.003652  3.130656  2.990548  2.839615  2.619118  2.464388  2.163315   \n", "0.003704  3.007749  2.865980  2.710516  2.490350  2.335700  2.038966   \n", "0.003756  3.253457  3.118540  2.973916  2.762662  2.614903  2.327204   \n", "0.003808  3.496552  3.368298  3.233781  3.031551  2.890701  2.612845   \n", "0.003860  3.593492  3.468442  3.337863  3.141014  3.004175  2.733829   \n", "0.003912  3.446482  3.320347  3.185242  2.990512  2.854986  2.592324   \n", "0.003964  3.444768  3.320406  3.186227  2.995983  2.863672  2.609014   \n", "0.004016  3.501379  3.379922  3.248631  3.063958  2.935695  2.689591   \n", "0.004068  3.275131  3.151205  3.014186  2.828878  2.699918  2.456900   \n", "0.004120  3.020942  2.894165  2.751033  2.564426  2.434296  2.193353   \n", "0.004172  2.544119  2.411750  2.257525  2.067351  1.934256  1.694733   \n", "0.004224  2.329228  2.195088  2.036857  1.846250  1.712691  1.475283   \n", "0.004276  2.313795  2.181016  2.024175  1.836182  1.704513  1.471009   \n", "0.004328  2.034383  1.899094  1.737088  1.547840  1.415085  1.182968   \n", "0.004380       NaN  1.471586  1.301448  1.109296  0.974184  0.742492   \n", "\n", "upper     0.004935  0.005028  0.005120  0.005213  0.005305  0.005398  \\\n", "lower                                                                  \n", "0.003392  1.979099  2.084045  2.444382  3.204094  3.652820  4.117634   \n", "0.003444  1.866005  1.962569  2.301861  3.015165  3.409682  3.817598   \n", "0.003496  1.922525  2.013699  2.334176  3.002872  3.355516  3.716485   \n", "0.003548  1.900004  1.987016  2.295468  2.937345  3.264196  3.598470   \n", "0.003600  1.734921  1.816221  2.112592  2.730371  3.029895  3.339268   \n", "0.003652  1.702375  1.779693  2.064459  2.656756  2.933725  3.220336   \n", "0.003704  1.581076  1.653431  1.926203  2.493769  2.746857  3.011203   \n", "0.003756  1.890631  1.965979  2.238486  2.800575  3.059509  3.324268   \n", "0.003808  2.197886  2.275156  2.545013  3.096964  3.357063  3.618156   \n", "0.003860  2.332325  2.408481  2.671687  3.207644  3.458870  3.709419   \n", "0.003912  2.198125  2.267652  2.514229  3.016404  3.238678  3.462903   \n", "0.003964  2.226443  2.292651  2.528556  3.007877  3.214558  3.423438   \n", "0.004016  2.320641  2.384426  2.611079  3.070088  3.264923  3.461502   \n", "0.004068  2.087991  2.146805  2.362848  2.801824  2.978415  3.159867   \n", "0.004120  1.822903  1.877069  2.083688  2.505258  2.665422  2.833609   \n", "0.004172  1.318432  1.365404  1.558485  1.955860  2.091610  2.240781   \n", "0.004224  1.099050  1.142714  1.328789  1.713122  1.837532  1.977492   \n", "0.004276  1.100961  1.143611  1.325783  1.701761  1.822128  1.957895   \n", "0.004328  0.811227  0.850277  1.025363  1.388554  1.497066  1.623542   \n", "0.004380  0.365780  0.400025  0.566433  0.914519  1.007619  1.122463   \n", "\n", "upper     0.005490  0.005583  0.005675  0.005768  0.005860  0.005953  \\\n", "lower                                                                  \n", "0.003392  4.246279  4.501882  4.229716  4.546329  4.418837  4.278275   \n", "0.003444  3.907485  4.094496  3.767662  3.996238  3.820562  3.615504   \n", "0.003496  3.780387  3.920027  3.579203  3.749979  3.561990  3.343230   \n", "0.003548  3.647758  3.762414  3.414638  3.558480  3.366155  3.143444   \n", "0.003600  3.372948  3.463249  3.100244  3.221073  3.020101  2.788078   \n", "0.003652  3.242987  3.314842  2.952164  3.054575  2.854816  2.625578   \n", "0.003704  3.022120  3.075525  2.708830  2.794341  2.593373  2.363842   \n", "0.003756  3.341257  3.401148  3.059121  3.147460  2.961122  2.749207   \n", "0.003808  3.638867  3.701693  3.382069  3.470101  3.296955  3.100889   \n", "0.003860  3.727925  3.785863  3.477331  3.559524  3.393194  3.205536   \n", "0.003912  3.469479  3.508945  3.201887  3.267938  3.103702  2.919358   \n", "0.003964  3.425219  3.456482  3.158365  3.216525  3.058028  2.880791   \n", "0.004016  3.460663  3.486809  3.200189  3.252868  3.101294  2.932346   \n", "0.004068  3.151684  3.167221  2.879010  2.923319  2.771612  2.602913   \n", "0.004120  2.818711  2.824733  2.534046  2.571069  2.418677  2.249538   \n", "0.004172  2.215507  2.207188  1.910134  1.936591  1.781720  1.610246   \n", "0.004224  1.948035  1.933754  1.636683  1.658556  1.504153  1.333442   \n", "0.004276  1.928169  1.913006  1.621851  1.642507  1.491506  1.324741   \n", "0.004328  1.589225  1.567688  1.275086  1.291030  1.139727  0.972841   \n", "0.004380  1.081721  1.051488  0.754610  0.764378  0.611386  0.442868   \n", "\n", "upper     0.006045  0.006138  \n", "lower                         \n", "0.003392  5.056115  4.465395  \n", "0.003444  4.196094  3.565650  \n", "0.003496  3.796147  3.209579  \n", "0.003548  3.543708  2.981330  \n", "0.003600  3.152014  2.596537  \n", "0.003652  2.953552  2.424950  \n", "0.003704  2.662727  2.152046  \n", "0.003756  3.038994  2.561536  \n", "0.003808  3.377576  2.932603  \n", "0.003860  3.466056  3.043844  \n", "0.003912  3.151763  2.752041  \n", "0.003964  3.095653  2.717419  \n", "0.004016  3.132675  2.775669  \n", "0.004068  2.790598  2.441862  \n", "0.004120  2.426835  2.083980  \n", "0.004172  1.773901  1.436257  \n", "0.004224  1.489952  1.157916  \n", "0.004276  1.476659  1.153199  \n", "0.004328  1.118138  0.798678  \n", "0.004380  0.580596  0.263577  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "# 1) Load your saved grid‐search results\n", "sweep = pd.read_csv(\"threshold_sweep_train.csv\")\n", "\n", "# 2) Pivot into matrix form\n", "pivot = sweep.pivot(index=\"lower\", columns=\"upper\", values=\"sharpe_train\")\n", "\n", "# 3) (Optional) inspect the pivot\n", "display(pivot)\n", "\n", "# 4) Plot the heatmap\n", "plt.figure()\n", "plt.imshow(pivot.values, aspect='auto')\n", "plt.xlabel(\"Upper Threshold\")\n", "plt.ylabel(\"Lower Threshold\")\n", "plt.title(\"Heatmap of Training Sharpe\")\n", "cbar = plt.colorbar(label=\"<PERSON> (Train)\")\n", "plt.xticks(ticks=range(len(pivot.columns)), labels=[f\"{c:.4f}\" for c in pivot.columns], rotation=90)\n", "plt.yticks(ticks=range(len(pivot.index)), labels=[f\"{r:.4f}\" for r in pivot.index])\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "4159c44a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Analyzed 96 trades over the most recent 5000 candles:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Entry Time</th>\n", "      <th>Exit Time</th>\n", "      <th>Direction</th>\n", "      <th>Entry Price</th>\n", "      <th>Exit Price</th>\n", "      <th>Return (%)</th>\n", "      <th>Duration (h)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-01-20 18:00:00+00:00</td>\n", "      <td>2025-01-20 19:00:00+00:00</td>\n", "      <td>Long</td>\n", "      <td>102933.687699</td>\n", "      <td>104048.879856</td>\n", "      <td>1.083408</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-01-21 02:00:00+00:00</td>\n", "      <td>2025-01-21 14:00:00+00:00</td>\n", "      <td>Long</td>\n", "      <td>101377.843082</td>\n", "      <td>104335.130390</td>\n", "      <td>2.917094</td>\n", "      <td>12.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-01-21 15:00:00+00:00</td>\n", "      <td>2025-01-22 13:00:00+00:00</td>\n", "      <td>Long</td>\n", "      <td>103153.457520</td>\n", "      <td>105321.284268</td>\n", "      <td>2.101555</td>\n", "      <td>22.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-01-22 14:00:00+00:00</td>\n", "      <td>2025-01-22 18:00:00+00:00</td>\n", "      <td>Long</td>\n", "      <td>104093.450630</td>\n", "      <td>104365.564593</td>\n", "      <td>0.261413</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2025-01-22 19:00:00+00:00</td>\n", "      <td>2025-01-22 20:00:00+00:00</td>\n", "      <td>Long</td>\n", "      <td>103997.774024</td>\n", "      <td>104594.321280</td>\n", "      <td>0.573615</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91</th>\n", "      <td>2025-05-29 19:00:00+00:00</td>\n", "      <td>2025-05-29 22:00:00+00:00</td>\n", "      <td>Long</td>\n", "      <td>105986.831048</td>\n", "      <td>106461.711800</td>\n", "      <td>0.448056</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92</th>\n", "      <td>2025-06-06 15:00:00+00:00</td>\n", "      <td>2025-06-06 18:00:00+00:00</td>\n", "      <td>Long</td>\n", "      <td>104871.444640</td>\n", "      <td>104979.431588</td>\n", "      <td>0.102971</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93</th>\n", "      <td>2025-06-09 14:00:00+00:00</td>\n", "      <td>2025-06-09 22:00:00+00:00</td>\n", "      <td>Long</td>\n", "      <td>107156.832685</td>\n", "      <td>109998.715254</td>\n", "      <td>2.652078</td>\n", "      <td>8.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>94</th>\n", "      <td>2025-06-09 23:00:00+00:00</td>\n", "      <td>2025-06-10 13:00:00+00:00</td>\n", "      <td>Long</td>\n", "      <td>110241.162223</td>\n", "      <td>109738.310991</td>\n", "      <td>-0.456137</td>\n", "      <td>14.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>95</th>\n", "      <td>2025-06-11 14:00:00+00:00</td>\n", "      <td>2025-06-11 15:00:00+00:00</td>\n", "      <td>Long</td>\n", "      <td>109718.257179</td>\n", "      <td>109912.762901</td>\n", "      <td>0.177277</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>96 rows × 7 columns</p>\n", "</div>"], "text/plain": ["                  Entry Time                 Exit Time Direction  \\\n", "0  2025-01-20 18:00:00+00:00 2025-01-20 19:00:00+00:00      Long   \n", "1  2025-01-21 02:00:00+00:00 2025-01-21 14:00:00+00:00      Long   \n", "2  2025-01-21 15:00:00+00:00 2025-01-22 13:00:00+00:00      Long   \n", "3  2025-01-22 14:00:00+00:00 2025-01-22 18:00:00+00:00      Long   \n", "4  2025-01-22 19:00:00+00:00 2025-01-22 20:00:00+00:00      Long   \n", "..                       ...                       ...       ...   \n", "91 2025-05-29 19:00:00+00:00 2025-05-29 22:00:00+00:00      Long   \n", "92 2025-06-06 15:00:00+00:00 2025-06-06 18:00:00+00:00      Long   \n", "93 2025-06-09 14:00:00+00:00 2025-06-09 22:00:00+00:00      Long   \n", "94 2025-06-09 23:00:00+00:00 2025-06-10 13:00:00+00:00      Long   \n", "95 2025-06-11 14:00:00+00:00 2025-06-11 15:00:00+00:00      Long   \n", "\n", "      Entry Price     Exit Price  Return (%)  Duration (h)  \n", "0   102933.687699  104048.879856    1.083408           1.0  \n", "1   101377.843082  104335.130390    2.917094          12.0  \n", "2   103153.457520  105321.284268    2.101555          22.0  \n", "3   104093.450630  104365.564593    0.261413           4.0  \n", "4   103997.774024  104594.321280    0.573615           1.0  \n", "..            ...            ...         ...           ...  \n", "91  105986.831048  106461.711800    0.448056           3.0  \n", "92  104871.444640  104979.431588    0.102971           3.0  \n", "93  107156.832685  109998.715254    2.652078           8.0  \n", "94  110241.162223  109738.310991   -0.456137          14.0  \n", "95  109718.257179  109912.762901    0.177277           1.0  \n", "\n", "[96 rows x 7 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Saved recent trade analysis to trade_analysis_recent5000.csv\n"]}], "source": []}, {"cell_type": "markdown", "id": "44c2804d", "metadata": {}, "source": ["## ETH data cleaninig"]}, {"cell_type": "code", "execution_count": 17, "id": "9f5a405e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved ETH OI + price indicator (20000 rows) to eth_oi_with_price_ratio.csv\n"]}], "source": ["import pandas as pd\n", "\n", "# 1) Load the ETH OI dataset (with price embedded)\n", "oi = pd.read_csv(\"eth_oi_20k_data.csv\", parse_dates=[\"datetime\"])\n", "\n", "# 2) Extract & rename columns\n", "df = oi[[\"datetime\", \"CLOSE_QUOTE\", \"CLOSE_SETTLEMENT\", \"CLOSE_MARK_PRICE\"]].copy()\n", "df.rename(columns={\n", "    \"CLOSE_QUOTE\":      \"open_interest_value\",\n", "    \"CLOSE_SETTLEMENT\": \"open_interest_count\",\n", "    \"CLOSE_MARK_PRICE\":  \"priceUsd\"\n", "}, inplace=True)\n", "\n", "# 3) Compute indicator & returns\n", "df[\"oi_price_ratio\"] = df[\"open_interest_value\"] / df[\"priceUsd\"]\n", "df[\"price_pct_change\"] = df[\"priceUsd\"].pct_change()\n", "\n", "# 4) Save to a new CSV\n", "output_file = \"eth_oi_with_price_ratio.csv\"\n", "df.to_csv(output_file, index=False)\n", "print(f\"Saved ETH OI + price indicator ({len(df)} rows) to {output_file}\")\n"]}, {"cell_type": "markdown", "id": "2361044b", "metadata": {}, "source": ["## ETH back tesing"]}, {"cell_type": "code", "execution_count": 37, "id": "47d0a041", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["lower     8.197268e+05\n", "upper     1.930530e+06\n", "sharpe    2.038347e+00\n", "Name: 319, dtype: float64\n", "=== ETH OI/Price Backtest (Full Dataset) ===\n", "Best thresholds: lower = 819726.784687, upper = 1930530.481930\n", "<PERSON> (full) = 2.038\n", "Total Return        = 305.19%\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "# 1) Load your ETH OI+price ratio data\n", "#    Change the filename if yours is different\n", "df = pd.read_csv(\"eth_oi_with_price_ratio.csv\", parse_dates=[\"datetime\"])\n", "df = df.sort_values(\"datetime\").reset_index(drop=True)\n", "\n", "# 2) Grid-search thresholds on the full dataset\n", "ratios = df[\"oi_price_ratio\"].dropna()\n", "lower_min, lower_max = ratios.quantile([0.01, 0.5])\n", "upper_min, upper_max = ratios.quantile([0.5, 0.99])\n", "lower_bounds = np.linspace(lower_min, lower_max, 20)\n", "upper_bounds = np.linspace(upper_min, upper_max, 20)\n", "\n", "results = []\n", "for low in lower_bounds:\n", "    for high in upper_bounds:\n", "        if low >= high:\n", "            continue\n", "        df[\"position\"] = np.where(\n", "            df[\"oi_price_ratio\"] < low, 1,\n", "            np.where(df[\"oi_price_ratio\"] > high, -1, 0)\n", "        )\n", "        strat_ret = df[\"position\"].shift(1) * df[\"price_pct_change\"]\n", "        ret = strat_ret.dropna()\n", "        if ret.std() == 0 or ret.empty:\n", "            continue\n", "        sharpe = ret.mean() / ret.std() * np.sqrt(365 * 24)\n", "        results.append({\"lower\": low, \"upper\": high, \"sharpe\": sharpe})\n", "\n", "results_df = pd.DataFrame(results)\n", "best = results_df.loc[results_df[\"sharpe\"].idxmax()]\n", "print(best)\n", "lower_best, upper_best = best[\"lower\"], best[\"upper\"]\n", "\n", "# 3) Apply best thresholds on full data\n", "df[\"position\"] = np.where(\n", "    df[\"oi_price_ratio\"] < lower_best, 1,\n", "    np.where(df[\"oi_price_ratio\"] > upper_best, -1, 0)\n", ")\n", "df[\"strat_ret\"] = df[\"position\"].shift(1) * df[\"price_pct_change\"]\n", "df[\"equity\"]    = (1 + df[\"strat_ret\"].fillna(0)).cumprod()\n", "\n", "# 4) Compute and print overall performance\n", "total_return = df[\"equity\"].iloc[-1] - 1\n", "sharpe_full  = df[\"strat_ret\"].mean() / df[\"strat_ret\"].std() * np.sqrt(365 * 24)\n", "\n", "print(\"=== ETH OI/Price Backtest (Full Dataset) ===\")\n", "print(f\"Best thresholds: lower = {lower_best:.6f}, upper = {upper_best:.6f}\")\n", "print(f\"<PERSON> (full) = {sharpe_full:.3f}\")\n", "print(f\"Total Return        = {total_return:.2%}\")\n", "\n", "# 5) Save results\n", "results_df.to_csv(\"eth_price_ratio_sweep_full.csv\", index=False)\n", "df.to_csv(\"eth_backtest_price_ratio_full.csv\", index=False)\n"]}, {"cell_type": "code", "execution_count": 31, "id": "5a259c0e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== ETH OI/Price Strategy with Fixed Thresholds ===\n", "Thresholds: lower = 819726.7846875, upper = 1930530.48193\n", "Total Return   = 305.19%\n", "<PERSON>   = 2.038\n", "Saved backtest results → eth_backtest_fixed_thresholds.csv\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "# 1) Load your ETH OI+price ratio data\n", "# Replace with your actual filename if different\n", "df = pd.read_csv(\"eth_oi_with_price_ratio.csv\", parse_dates=[\"datetime\"])\n", "df = df.sort_values(\"datetime\").reset_index(drop=True)\n", "\n", "# 2) Define your fixed thresholds (replace with your chosen values)\n", "lower_threshold = 819726.7846875  # example: set to your fixed lower threshold\n", "upper_threshold = 1930530.481930  # example: set to your fixed upper threshold\n", "\n", "# 3) Generate positions: +1 long when ratio < lower, -1 short when ratio > upper\n", "df[\"position\"] = np.where(\n", "    df[\"oi_price_ratio\"] < lower_threshold, 1,\n", "    np.where(df[\"oi_price_ratio\"] > upper_threshold, -1, 0)\n", ")\n", "\n", "# 4) Compute strategy returns and equity curve\n", "df[\"strat_ret\"] = df[\"position\"].shift(1) * df[\"price_pct_change\"]\n", "df[\"equity\"]    = (1 + df[\"strat_ret\"].fillna(0)).cumprod()\n", "\n", "# 5) Compute performance metrics\n", "total_return = df[\"equity\"].iloc[-1] - 1\n", "sharpe       = df[\"strat_ret\"].mean() / df[\"strat_ret\"].std() * np.sqrt(365 * 24)\n", "\n", "print(\"=== ETH OI/Price Strategy with Fixed Thresholds ===\")\n", "print(f\"Thresholds: lower = {lower_threshold}, upper = {upper_threshold}\")\n", "print(f\"Total Return   = {total_return:.2%}\")\n", "print(f\"Sharpe Ratio   = {sharpe:.3f}\")\n", "\n", "# 6) Save the result for review\n", "output_file = \"eth_backtest_fixed_thresholds.csv\"\n", "df.to_csv(output_file, index=False)\n", "print(f\"Saved backtest results → {output_file}\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "6e413c3d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'symbol': 'BTC/USDT:USDT', 'baseVolume': 78338.916, 'quoteVolume': None, 'openInterestAmount': 78338.916, 'openInterestValue': None, 'timestamp': 1750179291322, 'datetime': '2025-06-17T16:54:51.322Z', 'info': {'symbol': 'BTCUSDT', 'openInterest': '78338.916', 'time': '1750179291322'}}\n"]}, {"ename": "KeyError", "evalue": "'openInterest'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[22], line 54\u001b[0m\n\u001b[0;32m     51\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;241m0\u001b[39m\n\u001b[0;32m     53\u001b[0m \u001b[38;5;66;03m# Quick test\u001b[39;00m\n\u001b[1;32m---> 54\u001b[0m oi, mc, ret, price \u001b[38;5;241m=\u001b[39m fetch_latest_metrics()\n\u001b[0;32m     55\u001b[0m sig \u001b[38;5;241m=\u001b[39m compute_signal(oi, mc)\n\u001b[0;32m     56\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mOI=\u001b[39m\u001b[38;5;132;01m{\u001b[39;00moi\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m, MC=\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mmc\u001b[38;5;132;01m:\u001b[39;00m\u001b[38;5;124m.2f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m, Rtn=\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mret\u001b[38;5;132;01m:\u001b[39;00m\u001b[38;5;124m.4%\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m, Price=\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mprice\u001b[38;5;132;01m:\u001b[39;00m\u001b[38;5;124m.2f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m, Signal=\u001b[39m\u001b[38;5;132;01m{\u001b[39;00msig\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n", "Cell \u001b[1;32mIn[22], line 32\u001b[0m, in \u001b[0;36mfetch_latest_metrics\u001b[1;34m()\u001b[0m\n\u001b[0;32m     30\u001b[0m oi_data \u001b[38;5;241m=\u001b[39m exchange\u001b[38;5;241m.\u001b[39mfetch_open_interest(SYMBOL)\n\u001b[0;32m     31\u001b[0m \u001b[38;5;28mprint\u001b[39m(oi_data)\n\u001b[1;32m---> 32\u001b[0m oi \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mfloat\u001b[39m(oi_data[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mopenInterest\u001b[39m\u001b[38;5;124m'\u001b[39m])\n\u001b[0;32m     34\u001b[0m \u001b[38;5;66;03m# 2) Price + return from last 2 hours\u001b[39;00m\n\u001b[0;32m     35\u001b[0m ohlcv \u001b[38;5;241m=\u001b[39m exchange\u001b[38;5;241m.\u001b[39mfetch_ohlcv(SYMBOL, timeframe\u001b[38;5;241m=\u001b[39mTIMEFRAME, limit\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m2\u001b[39m)\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON>r\u001b[0m: 'openInterest'"]}], "source": ["API_KEY    = \"k4qiTrIPpKFxW0Zu5fCUQ3rs8GrAQFrUSO9tRN9UnnAvCs6ZoPj6gS50Mh8Ki5tg\"\n", "API_SECRET = \"ERT4TPVRNW21ghwBV5z2St2GcFVihEN81pAjESaCGzIxKtG5WTm6DLSMq3wMrD8M\"\n", "import os, time\n", "import pandas as pd\n", "import numpy as np\n", "import ccxt\n", "from datetime import datetime, timezone\n", "\n", "# ─── SETUP ─────────────────────────────────────────────────────────────────────\n", "exchange = ccxt.binance({\n", "    'apiKey': API_KEY,\n", "    'secret': API_SECRET,\n", "    'enableRateLimit': True,\n", "    'options': {\n", "        'defaultType': 'future',        # ← tell CCXT to use the futures API\n", "    },\n", "})\n", "\n", "SYMBOL     = \"BTC/USDT\"\n", "TIMEFRAME  = '1h'\n", "LOT_USDT   = 100\n", "LOW_TH     = 0.003554\n", "UP_TH      = 0.004290\n", "\n", "# Load circulating‐supply history for market‑cap\n", "supply = pd.read_csv(\"btc_20k_1h_marketcap.csv\", parse_dates=[\"datetime\"]).set_index(\"datetime\")\n", "\n", "def fetch_latest_metrics():\n", "    # 1) Open Interest (futures)\n", "    oi_data = exchange.fetch_open_interest(SYMBOL)\n", "    print(oi_data)\n", "    oi = float(oi_data[\"info\"]['openInterest'])\n", "    \n", "    # 2) Price + return from last 2 hours\n", "    ohlcv = exchange.fetch_ohlcv(SYMBOL, timeframe=TIMEFRAME, limit=2)\n", "    prev, last = ohlcv[0], ohlcv[1]\n", "    price_ret = last[4] / prev[4] - 1\n", "    price     = last[4]\n", "    \n", "    # 3) Market‑cap = price * supply at timestamp\n", "    ts = datetime.utcfromtimestamp(last[0]/1000).replace(tzinfo=timezone.utc)\n", "    circ = supply.loc[ts, \"circulatingSupply\"]\n", "    mc   = circ * price\n", "    \n", "    return oi, mc, price_ret, price\n", "\n", "def compute_signal(oi, mc):\n", "    r = oi / mc\n", "    if r < LOW_TH: return -1\n", "    if r > UP_TH:  return +1\n", "    return 0\n", "\n", "# Quick test\n", "oi, mc, ret, price = fetch_latest_metrics()\n", "sig = compute_signal(oi, mc)\n", "print(f\"OI={oi}, MC={mc:.2f}, Rtn={ret:.4%}, Price={price:.2f}, Signal={sig}\")\n"]}, {"cell_type": "code", "execution_count": 59, "id": "765a4f32", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Best oi/price thresholds (trained):\n", "  Lower = 67095.783790, Upper = 85390.236500\n", "\n", "Training | Sharpe: 1.179, Total Return: 76.46%\n", "Validation | Sharpe: -0.658, Total Return: -13.32%\n", "Testing | Sharpe: nan, Total Return: 0.00%\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "# 1) Load the merged data with oi_price_ratio\n", "# df = pd.read_csv(\"merged_with_indicators.csv\", parse_dates=[\"datetime\"])\n", "# df = df.sort_values(\"datetime\").reset_index(drop=True)\n", "df = pd.read_csv(\"data/BTC_oi_price.csv\", parse_dates=[\"datetime\"])\n", "df = df.sort_values(\"datetime\").reset_index(drop=True)\n", "df[\"oi_price_ratio\"]  = df[\"open_interest_value\"] / df[\"priceUsd\"]\n", "df[\"price_pct_change\"] = df[\"priceUsd\"].pct_change()\n", "\n", "\n", "# 2) Define split sizes\n", "n_valid = 5000   # adjust as you like\n", "n_test  = 2\n", "train_df = df.iloc[:-(n_valid + n_test)].copy()\n", "valid_df = df.iloc[-(n_valid + n_test):-n_test].copy()\n", "test_df  = df.iloc[-n_test:].copy()\n", "full_df  = df.copy()\n", "\n", "# 3) Build your threshold grid on the training split\n", "ratios = train_df[\"oi_price_ratio\"].dropna()\n", "lower_min, lower_max = ratios.quantile([0.01, 0.5])\n", "upper_min, upper_max = ratios.quantile([0.5, 0.99])\n", "lower_bounds = np.linspace(lower_min, lower_max, 20)\n", "upper_bounds = np.linspace(upper_min, upper_max, 20)\n", "\n", "results = []\n", "for low in lower_bounds:\n", "    for high in upper_bounds:\n", "        if low >= high:\n", "            continue\n", "        train_df[\"position\"] = np.where(\n", "            train_df[\"oi_price_ratio\"] < low, -1,\n", "            np.where(train_df[\"oi_price_ratio\"] > high, 1, 0)\n", "        )\n", "        strat_ret = train_df[\"position\"].shift(1) * train_df[\"price_pct_change\"]\n", "        # dropna to avoid zero‐std problems\n", "        valid_rets = strat_ret.dropna()\n", "        if valid_rets.std() == 0 or valid_rets.empty:\n", "            continue\n", "        sharpe = valid_rets.mean() / valid_rets.std() * np.sqrt(365 * 24)\n", "        results.append({\"lower\": low, \"upper\": high, \"sharpe_train\": sharpe})\n", "\n", "results_df = pd.DataFrame(results)\n", "best = results_df.loc[results_df[\"sharpe_train\"].idxmax()]\n", "lower_best, upper_best = best[\"lower\"], best[\"upper\"]\n", "\n", "# 4) Apply best thresholds to all splits\n", "for subset in (train_df, valid_df, test_df, full_df):\n", "    subset[\"position\"] = np.where(\n", "        subset[\"oi_price_ratio\"] < lower_best, -1,\n", "        np.where(subset[\"oi_price_ratio\"] > upper_best, 1, 0)\n", "    )\n", "    subset[\"strat_ret\"] = subset[\"position\"].shift(1) * subset[\"price_pct_change\"]\n", "    subset[\"equity\"]    = (1 + subset[\"strat_ret\"].fillna(0)).cumprod()\n", "\n", "# 5) Print metrics\n", "def compute_metrics(df_, name):\n", "    if df_.empty:\n", "        print(f\"{name}: no data\")\n", "        return\n", "    total_ret = df_[\"equity\"].iloc[-1] - 1\n", "    sharpe    = df_[\"strat_ret\"].mean() / df_[\"strat_ret\"].std() * np.sqrt(365 * 24)\n", "    print(f\"{name} | Sharpe: {sharpe:.3f}, Total Return: {total_ret:.2%}\")\n", "\n", "print(\"Best oi/price thresholds (trained):\")\n", "print(f\"  Lower = {lower_best:.6f}, Upper = {upper_best:.6f}\\n\")\n", "\n", "compute_metrics(train_df, \"Training\")\n", "compute_metrics(valid_df, \"Validation\")\n", "compute_metrics(test_df,  \"Testing\")\n", "\n", "# 6) Save results\n", "results_df.to_csv(\"price_ratio_threshold_sweep.csv\", index=False)\n", "full_df.to_csv(\"backtest_price_ratio.csv\", index=False)\n"]}, {"cell_type": "code", "execution_count": 54, "id": "aae41e22", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== Fixed-T<PERSON><PERSON><PERSON> Backtest ===\n", "Thresholds: lower = 66976.966058, upper = 85378.351227\n", "Total Return = 1986.99%\n", "<PERSON> = 4.308\n", "\n", "Saved detailed backtest to backtest_fixed_thresholds.csv\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "# --- USER CONFIGURATION ---\n", "# Replace with your actual merged filename if different\n", "input_file = \"merged_with_indicators.csv\"\n", "\n", "# Set your fixed OI/market-cap thresholds here:\n", "lower_threshold = 66976.966058  # example lower threshold\n", "upper_threshold = 85378.351227  # example upper threshold\n", "\n", "# --- LOAD DATA ---\n", "df = pd.read_csv(input_file, parse_dates=[\"datetime\"])\n", "df = df.sort_values(\"datetime\").reset_index(drop=True)\n", "\n", "# --- APPLY FIXED THRESHOLDS ---\n", "# +1 long when ratio > upper_threshold, -1 short when ratio < lower_threshold, 0 otherwise\n", "df[\"position\"] = np.where(\n", "    df[\"oi_price_ratio\"] > upper_threshold, 1,\n", "    np.where(df[\"oi_price_ratio\"] < lower_threshold, -1, 0)\n", ")\n", "\n", "# --- CALCULATE RETURNS & EQUITY CURVE ---\n", "df[\"strat_ret\"] = df[\"position\"].shift(1) * df[\"price_pct_change\"]\n", "df[\"equity\"]    = (1 + df[\"strat_ret\"].fillna(0)).cumprod()\n", "\n", "# --- PERFORMANC<PERSON> METRICS ---\n", "total_return = df[\"equity\"].iloc[-1] - 1\n", "sharpe_ratio = df[\"strat_ret\"].mean() / df[\"strat_ret\"].std() * np.sqrt(365 * 24)\n", "\n", "print(\"=== Fixed-Threshold Backtest ===\")\n", "print(f\"Thresholds: lower = {lower_threshold}, upper = {upper_threshold}\")\n", "print(f\"Total Return = {total_return:.2%}\")\n", "print(f\"Sharpe Ratio = {sharpe_ratio:.3f}\")\n", "\n", "# --- SAVE RESULTS ---\n", "output_csv = \"backtest_fixed_thresholds.csv\"\n", "df.to_csv(output_csv, index=False)\n", "print(f\"\\nSaved detailed backtest to {output_csv}\")\n"]}, {"cell_type": "code", "execution_count": 6, "id": "a5b4b6bd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fetching OI+price for BTC...\n", " → Saved 20000 rows to data\\BTC_oi_price.csv\n", "\n", "Fetching OI+price for ETH...\n", " → Saved 20000 rows to data\\ETH_oi_price.csv\n", "\n", "Fetching OI+price for BNB...\n", " → Saved 20000 rows to data\\BNB_oi_price.csv\n", "\n", "Fetching OI+price for SOL...\n", " → Saved 20000 rows to data\\SOL_oi_price.csv\n", "\n", "Fetching OI+price for ADA...\n", " → Saved 20000 rows to data\\ADA_oi_price.csv\n", "\n", "Fetching OI+price for DOGE...\n", " → Saved 20000 rows to data\\DOGE_oi_price.csv\n", "\n", "All coins fetched and saved in 'data/' directory.\n"]}], "source": ["import time\n", "import requests\n", "import pandas as pd\n", "from pathlib import Path\n", "\n", "# --- CONFIGURATION ---\n", "COINS       = [\"BTC\", \"ETH\", \"BNB\", \"SOL\", \"ADA\", \"DOGE\"]      # coins you want\n", "VS           = \"USDT\"\n", "OI_MARKET    = \"binance\"\n", "OI_ENDPOINT  = \"https://data-api.coindesk.com/futures/v1/historical/open-interest/hours\"\n", "BATCHES      = 10                           # 10 × 2000 points = 20 000 hours\n", "OUTPUT_DIR   = Path(\"data/\")\n", "PAUSE_SEC    = 0.2                          # throttle between calls\n", "\n", "OUTPUT_DIR.mkdir(exist_ok=True)\n", "\n", "def fetch_oi_markprice(coin: str) -> pd.DataFrame:\n", "    \"\"\"\n", "    Pulls hourly open-interest + mark-price data for the last (BATCHES*2000) hours.\n", "    Returns a DataFrame with columns: datetime, open_interest_value, priceUsd\n", "    \"\"\"\n", "    records = []\n", "    to_ts = int(time.time())\n", "    for batch in range(BATCHES):\n", "        params = {\n", "            \"market\": OI_MARKET,\n", "            \"instrument\": f\"{coin}-{VS}-VANILLA-PERPETUAL\",\n", "            \"groups\": \"ID,MAPPING,OHLC,OHLC_MESSAGE,MESSAGE\",\n", "            \"limit\": 2000,\n", "            \"aggregate\": 1,\n", "            \"fill\": \"true\",\n", "            \"apply_mapping\": \"true\",\n", "            \"to_ts\": to_ts\n", "        }\n", "        resp = requests.get(OI_ENDPOINT, params=params)\n", "        resp.raise_for_status()\n", "        data = resp.json().get(\"Data\", [])\n", "        if not data:\n", "            print(f\"No more data at batch {batch}.\")\n", "            break\n", "        records.extend(data)\n", "        # prepare next batch just before earliest timestamp\n", "        to_ts = min(item[\"TIMESTAMP\"] for item in data) - 1\n", "        time.sleep(PAUSE_SEC)\n", "\n", "    # Build DataFrame\n", "    df = pd.DataFrame(records)\n", "    df[\"datetime\"] = pd.to_datetime(df[\"TIMESTAMP\"], unit=\"s\")\n", "    df = (\n", "        df\n", "        .rename(columns={\n", "            \"CLOSE_QUOTE\":      \"open_interest_value\",\n", "            \"CLOSE_MARK_PRICE\": \"priceUsd\"\n", "        })\n", "        .loc[:, [\"datetime\", \"open_interest_value\", \"priceUsd\"]]\n", "        .sort_values(\"datetime\")\n", "        .reset_index(drop=True)\n", "    )\n", "    return df\n", "\n", "# Fetch & save for each coin\n", "for coin in COINS:\n", "    out_path = OUTPUT_DIR / f\"{coin}_oi_price.csv\"\n", "    if out_path.exists():\n", "        print(f\"→ {out_path.name} already exists. Skipping {coin}.\")\n", "        continue\n", "\n", "    print(f\"Fetching OI+price for {coin}...\")\n", "    df_coin = fetch_oi_markprice(coin)\n", "    df_coin.to_csv(out_path, index=False)\n", "    print(f\" → Saved {len(df_coin)} rows to {out_path}\\n\")\n", "\n", "print(\"All coins fetched and saved in 'data/' directory.\")\n"]}, {"cell_type": "code", "execution_count": 7, "id": "e4ac7edf", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\n", "KeyboardInterrupt\n", "\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "\n", "# ── CONFIG ────────────────────────────────────────────────────────────\n", "COINS      = [\"BTC\",\"ETH\",\"BNB\",\"SOL\",\"ADA\",\"DOGE\"]\n", "INPUT_DIR  = Path(\"./data\")\n", "OUTPUT_DIR = Path(\"./results_multi_full\")\n", "OUTPUT_DIR.mkdir(parents=True, exist_ok=True)\n", "\n", "summary = []\n", "\n", "for coin in COINS:\n", "    # 1) Load + indicators\n", "    df = pd.read_csv(INPUT_DIR / f\"{coin}_oi_price.csv\", parse_dates=[\"datetime\"])\n", "    df = df.sort_values(\"datetime\").reset_index(drop=True)\n", "    df[\"oi_price_ratio\"]   = df[\"open_interest_value\"] / df[\"priceUsd\"]\n", "    df[\"price_pct_change\"] = df[\"priceUsd\"].pct_change()\n", "\n", "    # 2) Grid-search on FULL DATA\n", "    ratios = df[\"oi_price_ratio\"].dropna()\n", "    lower_min, lower_max = ratios.quantile([0.01, 0.5])\n", "    upper_min, upper_max = ratios.quantile([0.5, 0.99])\n", "    lower_grid = np.linspace(lower_min, lower_max, 20)\n", "    upper_grid = np.linspace(upper_min, upper_max, 20)\n", "\n", "    grid = []\n", "    for low in lower_grid:\n", "        for high in upper_grid:\n", "            if low >= high:\n", "                continue\n", "            df[\"position\"] = np.where(df[\"oi_price_ratio\"] < low, 1,\n", "                               np.where(df[\"oi_price_ratio\"] > high, -1, 0))\n", "            rets = (df[\"position\"].shift(1) * df[\"price_pct_change\"]).dropna()\n", "            if rets.std() == 0 or rets.empty:\n", "                continue\n", "            sharpe = rets.mean() / rets.std() * np.sqrt(365 * 24)\n", "            grid.append({\"lower\": low, \"upper\": high, \"sharpe\": sharpe})\n", "\n", "    grid_df    = pd.DataFrame(grid)\n", "    best       = grid_df.loc[grid_df[\"sharpe\"].idxmax()]\n", "    low_best   = best[\"lower\"]\n", "    high_best  = best[\"upper\"]\n", "    grid_df.to_csv(OUTPUT_DIR / f\"{coin}_threshold_grid_full.csv\", index=False)\n", "\n", "    # 3) Full backtest with best thresholds\n", "    df[\"position\"]  = np.where(df[\"oi_price_ratio\"] < low_best, 1,\n", "                       np.where(df[\"oi_price_ratio\"] > high_best, -1, 0))\n", "    df[\"strat_ret\"] = df[\"position\"].shift(1) * df[\"price_pct_change\"]\n", "    df[\"equity\"]    = (1 + df[\"strat_ret\"].fillna(0)).cumprod()\n", "    df.to_csv(OUTPUT_DIR / f\"{coin}_backtest_full.csv\", index=False)\n", "\n", "    # 4) Compute metrics\n", "    sharpe_full = df[\"strat_ret\"].mean() / df[\"strat_ret\"].std() * np.sqrt(365 * 24)\n", "    full_return = df[\"equity\"].iloc[-1] - 1\n", "\n", "    # 5) Max Drawdown\n", "    running_max    = df[\"equity\"].cummax()\n", "    drawdowns      = (df[\"equity\"] / running_max) - 1\n", "    max_drawdown   = drawdowns.min()  # this will be a negative number\n", "\n", "    summary.append({\n", "        \"coin\":         coin,\n", "        \"lower_th\":     low_best,\n", "        \"upper_th\":     high_best,\n", "        \"sharpe_full\":  sharpe_full,\n", "        \"full_return\":  full_return,\n", "        \"max_drawdown\": max_drawdown\n", "    })\n", "\n", "# 6) Save overall summary\n", "pd.DataFrame(summary).to_csv(OUTPUT_DIR / \"summary_metrics_full.csv\", index=False)\n", "print(\"Grid search + full backtest + drawdown complete. Results saved to\", OUTPUT_DIR)\n"]}, {"cell_type": "code", "execution_count": 10, "id": "e86f4f0c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Grid search + full backtest complete. Results saved to results_multi_full\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "\n", "# ── CONFIG ────────────────────────────────────────────────────────────\n", "COINS      = [\"BTC\",\"ETH\",\"BNB\",\"SOL\",\"ADA\",\"DOGE\"]\n", "INPUT_DIR  = Path(\"./data\")\n", "OUTPUT_DIR = Path(\"./results_multi_full\")\n", "OUTPUT_DIR.mkdir(parents=True, exist_ok=True)\n", "\n", "summary = []\n", "\n", "for coin in COINS:\n", "    # 1) Load + indicators\n", "    df = pd.read_csv(INPUT_DIR / f\"{coin}_oi_price.csv\", parse_dates=[\"datetime\"])\n", "    df = df.sort_values(\"datetime\").reset_index(drop=True)\n", "    df[\"oi_price_ratio\"]   = df[\"open_interest_value\"] / df[\"priceUsd\"]\n", "    df[\"price_pct_change\"] = df[\"priceUsd\"].pct_change()\n", "\n", "    # 2) Grid-search ON FULL DATA to pick thresholds\n", "    ratios = df[\"oi_price_ratio\"].dropna()\n", "    lower_min, lower_max = ratios.quantile([0.01, 0.5])\n", "    upper_min, upper_max = ratios.quantile([0.5, 0.99])\n", "    lower_grid = np.linspace(lower_min, lower_max, 20)\n", "    upper_grid = np.linspace(upper_min, upper_max, 20)\n", "\n", "    grid = []\n", "    for low in lower_grid:\n", "        for high in upper_grid:\n", "            if low >= high:\n", "                continue\n", "            df[\"position\"] = np.where(df[\"oi_price_ratio\"] < low, 1,\n", "                               np.where(df[\"oi_price_ratio\"] > high, -1, 0))\n", "            rets = (df[\"position\"].shift(1) * df[\"price_pct_change\"]).dropna()\n", "            if rets.std() == 0 or rets.empty:\n", "                continue\n", "            sharpe = rets.mean() / rets.std() * np.sqrt(365*24)\n", "            grid.append({\"lower\": low, \"upper\": high, \"sharpe\": sharpe})\n", "\n", "    grid_df = pd.DataFrame(grid)\n", "    best    = grid_df.loc[grid_df[\"sharpe\"].idxmax()]\n", "    low_best, high_best = best[\"lower\"], best[\"upper\"]\n", "    grid_df.to_csv(OUTPUT_DIR / f\"{coin}_threshold_grid_full.csv\", index=False)\n", "\n", "    # 3) Back-test with those thresholds (same full series)\n", "    df[\"position\"]  = np.where(df[\"oi_price_ratio\"] < low_best, 1,\n", "                        np.where(df[\"oi_price_ratio\"] > high_best, -1, 0))\n", "    df[\"strat_ret\"] = df[\"position\"].shift(1) * df[\"price_pct_change\"]\n", "    df[\"equity\"]    = (1 + df[\"strat_ret\"].fillna(0)).cumprod()\n", "    df.to_csv(OUTPUT_DIR / f\"{coin}_backtest_full.csv\", index=False)\n", "\n", "    sharpe_full = df[\"strat_ret\"].mean() / df[\"strat_ret\"].std() * np.sqrt(365*24)\n", "    full_return = df[\"equity\"].iloc[-1] - 1\n", "\n", "    summary.append({\n", "        \"coin\":       coin,\n", "        \"lower_th\":   low_best,\n", "        \"upper_th\":   high_best,\n", "        \"sharpe_full\":sharpe_full,\n", "        \"full_return\":full_return\n", "    })\n", "\n", "# 4) Save overall summary\n", "pd.DataFrame(summary).to_csv(OUTPUT_DIR / \"summary_metrics_full.csv\", index=False)\n", "print(\"Grid search + full backtest complete. Results saved to\", OUTPUT_DIR)\n"]}, {"cell_type": "code", "execution_count": 4, "id": "d58b8d10", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Reverse grid + backtest + drawdown complete. Results saved to results_multi_reverse\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "\n", "# ── CONFIG ────────────────────────────────────────────────────────────\n", "COINS      = [\"BTC\",\"ETH\",\"BNB\",\"SOL\",\"ADA\",\"XRP\",\"LINK\",\"DOGE\",\"BCH\"]\n", "INPUT_DIR  = Path(\"./data\")\n", "OUTPUT_DIR = Path(\"./results_multi_reverse\")\n", "OUTPUT_DIR.mkdir(parents=True, exist_ok=True)\n", "\n", "summary = []\n", "\n", "for coin in COINS:\n", "    # 1) Load + indicators\n", "    df = pd.read_csv(INPUT_DIR / f\"{coin}_oi_price.csv\", parse_dates=[\"datetime\"])\n", "    df = df.sort_values(\"datetime\").reset_index(drop=True)\n", "    df[\"oi_price_ratio\"]   = df[\"open_interest_value\"] / df[\"priceUsd\"]\n", "    df[\"price_pct_change\"] = df[\"priceUsd\"].pct_change()\n", "\n", "    # 2) Grid-search ON FULL DATA to pick thresholds (REVERSED logic)\n", "    ratios = df[\"oi_price_ratio\"].dropna()\n", "    lower_min, lower_max = ratios.quantile([0.01, 0.5])\n", "    upper_min, upper_max = ratios.quantile([0.5, 0.99])\n", "    lower_grid = np.linspace(lower_min, lower_max, 20)\n", "    upper_grid = np.linspace(upper_min, upper_max, 20)\n", "\n", "    grid = []\n", "    for low in lower_grid:\n", "        for high in upper_grid:\n", "            if low >= high:\n", "                continue\n", "            # reverse: long when OI/price < low, short when > high\n", "            df[\"position\"] = np.where(\n", "                df[\"oi_price_ratio\"] < low,  -1,\n", "                np.where(df[\"oi_price_ratio\"] > high, 1, 0)\n", "            )\n", "            rets = (df[\"position\"].shift(1) * df[\"price_pct_change\"]).dropna()\n", "            if rets.std() == 0 or rets.empty:\n", "                continue\n", "            sharpe = rets.mean() / rets.std() * np.sqrt(365 * 24)\n", "            grid.append({\"lower\": low, \"upper\": high, \"sharpe\": sharpe})\n", "\n", "    grid_df = pd.DataFrame(grid)\n", "    best    = grid_df.loc[grid_df[\"sharpe\"].idxmax()]\n", "    low_best, high_best = best[\"lower\"], best[\"upper\"]\n", "    grid_df.to_csv(OUTPUT_DIR / f\"{coin}_threshold_grid_reverse_full.csv\", index=False)\n", "\n", "    # 3) Back-test with those thresholds (same full series)\n", "    df[\"position\"]  = np.where(\n", "        df[\"oi_price_ratio\"] < low_best,  -1,\n", "        np.where(df[\"oi_price_ratio\"] > high_best, 1, 0)\n", "    )\n", "    df[\"strat_ret\"] = df[\"position\"].shift(1) * df[\"price_pct_change\"]\n", "    df[\"equity\"]    = (1 + df[\"strat_ret\"].fillna(0)).cumprod()\n", "    df.to_csv(OUTPUT_DIR / f\"{coin}_backtest_reverse_full.csv\", index=False)\n", "\n", "    # 4) Compute full-period Sharpe & Return\n", "    sharpe_full = df[\"strat_ret\"].mean() / df[\"strat_ret\"].std() * np.sqrt(365 * 24)\n", "    full_return = df[\"equity\"].iloc[-1] - 1\n", "\n", "    # 5) Compute Max Drawdown\n", "    running_max  = df[\"equity\"].cummax()\n", "    drawdowns    = (df[\"equity\"] / running_max) - 1\n", "    max_drawdown = drawdowns.min()\n", "\n", "    summary.append({\n", "        \"coin\":         coin,\n", "        \"lower_th\":     low_best,\n", "        \"upper_th\":     high_best,\n", "        \"sharpe_full\":  sharpe_full,\n", "        \"full_return\":  full_return,\n", "        \"max_drawdown\": max_drawdown\n", "    })\n", "\n", "# 6) Save overall summary\n", "pd.DataFrame(summary).to_csv(OUTPUT_DIR / \"summary_metrics_reverse_full.csv\", index=False)\n", "print(\"Reverse grid + backtest + drawdown complete. Results saved to\", OUTPUT_DIR)\n"]}, {"cell_type": "code", "execution_count": 70, "id": "74d673b0", "metadata": {}, "outputs": [], "source": ["def tg_send(bot_token: str, chat_id: str, text: str):\n", "    url = f\"https://api.telegram.org/bot{bot_token}/sendMessage\"\n", "    requests.post(url, json={\"chat_id\": chat_id, \"text\": text, \"parse_mode\": \"Markdown\"})\n", "    \n", "tg_send(\"7662733852:AAGABuq0QF0hCJu-6cfxArDNsVa1hoZOctc\", \"1748672341\", \"hello my test sending message with api\")\n"]}, {"cell_type": "code", "execution_count": 5, "id": "8d95b925", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved heatmap for BTC to results_multi_full\\BTC_sharpe_heatmap.png\n", "Saved heatmap for ETH to results_multi_full\\ETH_sharpe_heatmap.png\n", "Saved heatmap for BNB to results_multi_full\\BNB_sharpe_heatmap.png\n", "Saved heatmap for SOL to results_multi_full\\SOL_sharpe_heatmap.png\n", "Saved heatmap for ADA to results_multi_full\\ADA_sharpe_heatmap.png\n", "Saved heatmap for XRP to results_multi_full\\XRP_sharpe_heatmap.png\n", "Saved heatmap for LINK to results_multi_full\\LINK_sharpe_heatmap.png\n", "Saved heatmap for DOGE to results_multi_full\\DOGE_sharpe_heatmap.png\n", "Saved heatmap for BCH to results_multi_full\\BCH_sharpe_heatmap.png\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "\n", "# ── CONFIG ────────────────────────────────────────────────────────────\n", "COINS      = [\"BTC\",\"ETH\",\"BNB\",\"SOL\",\"ADA\",\"XRP\",\"LINK\",\"DOGE\",\"BCH\"]\n", "INPUT_DIR  = Path(\"./results_multi_full\")\n", "OUTPUT_DIR = INPUT_DIR  # save heatmaps alongside the CSVs\n", "\n", "# ── Loop & plot ────────────────────────────────────────────────────────\n", "for coin in COINS:\n", "    csv_path = INPUT_DIR / f\"{coin}_threshold_grid_full.csv\"\n", "    if not csv_path.exists():\n", "        print(f\"Grid CSV for {coin} not found at {csv_path}\")\n", "        continue\n", "\n", "    # 1) load grid\n", "    df = pd.read_csv(csv_path)\n", "    # 2) pivot into matrix\n", "    heat = df.pivot(index=\"lower\", columns=\"upper\", values=\"sharpe\")\n", "    # sort axes for nicer visuals\n", "    heat = heat.sort_index(ascending=False)\n", "\n", "    # 3) plot\n", "    plt.figure(figsize=(6,5))\n", "    plt.title(f\"{coin} OI/Price Sharpe Heatmap\")\n", "    plt.xlabel(\"upper threshold\")\n", "    plt.ylabel(\"lower threshold\")\n", "    # imshow expects array; extent maps x/y values\n", "    arr = heat.values\n", "    x0, x1 = heat.columns.min(), heat.columns.max()\n", "    y0, y1 = heat.index.min(),   heat.index.max()\n", "    plt.imshow(arr, \n", "               aspect=\"auto\",\n", "               origin=\"lower\",\n", "               extent=[x0, x1, y0, y1])\n", "    plt.colorbar(label=\"Sharpe Ratio\")\n", "    plt.tight_layout()\n", "\n", "    # 4) save\n", "    out_png = OUTPUT_DIR / f\"{coin}_sharpe_heatmap.png\"\n", "    plt.savefig(out_png, dpi=150)\n", "    plt.close()\n", "    print(f\"Saved heatmap for {coin} to {out_png}\")\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}