<#
.SYNOPSIS
    Build the trading-bot image and push to Amazon ECR.
.PARAMETER Version
    Optional image tag (defaults to YYYYMMDD-HHmmss).
#>

param(
    [string]$Version = (Get-Date -Format "yyyyMMdd-HHmmss")
)

$Region     = "ap-southeast-1"
$AccountId  = (aws sts get-caller-identity --query Account --output text)
$RepoUri    = "$AccountId.dkr.ecr.$Region.amazonaws.com/trading-bot"

Write-Host "Building version $Version for $RepoUri`n"

docker buildx build --platform linux/arm64 `
    -t "trading-bot:$Version" -t "trading-bot:latest" .

# ---------- ECR login ----------
aws ecr get-login-password --region $Region |
    docker login --username AWS --password-stdin "$AccountId.dkr.ecr.$Region.amazonaws.com"

# ---------- Tag & push ----------
# ---- tag ----
docker tag "trading-bot:$Version" "$( $RepoUri ):$Version"
docker tag "trading-bot:$Version" "$( $RepoUri ):latest"

# ---- push ----
docker push "$( $RepoUri ):$Version"
docker push "$( $RepoUri ):latest"


Write-Host "`n✅  Pushed $Version and latest to $RepoUri"
