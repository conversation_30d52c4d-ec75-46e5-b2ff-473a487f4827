"""
Basic Trading Strategy Templates for Cryptocurrency Backtesting

This module provides template functions for implementing trading strategies.
All specific strategy implementations have been removed - use this as a template
to implement your own profitable strategies.
Uses pandas for technical indicators instead of talib.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional


def add_technical_indicators(df: pd.DataFrame) -> pd.DataFrame:
    """
    Template function for adding technical indicators to the DataFrame.

    This is a template - implement your own technical indicators here.
    Uses pandas for calculations instead of talib.

    Args:
        df: DataFrame with OHLCV data

    Returns:
        DataFrame with added technical indicators
    """
    # Make a copy to avoid modifying the original DataFrame
    df = df.copy()

    # TODO: Add your technical indicators here
    # Example using pandas:
    # df['SMA20'] = df['Close'].rolling(window=20).mean()
    # df['RSI14'] = calculate_rsi_pandas(df['Close'], period=14)

    return df


def calculate_rsi_pandas(prices: pd.Series, period: int = 14) -> pd.Series:
    """
    Calculate RSI using pandas (example helper function).

    Args:
        prices: Price series
        period: RSI period

    Returns:
        RSI values
    """
    delta = prices.diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    avg_gain = gain.rolling(window=period).mean()
    avg_loss = loss.rolling(window=period).mean()
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    return rsi


def strategy_template_pandas(df: pd.DataFrame, **kwargs) -> pd.DataFrame:
    """
    Template function for implementing trading strategies using pandas.

    This is a template - implement your own profitable strategy here.
    The strategy should add a 'signal' column with values:
    - 1 for buy signals
    - -1 for sell signals
    - 0 for no signal/hold

    Args:
        df: DataFrame with OHLCV data
        **kwargs: Strategy-specific parameters

    Returns:
        DataFrame with added signal column
    """
    # Make a copy to avoid modifying the original DataFrame
    df = df.copy()

    # Initialize signal column
    df["signal"] = 0

    # TODO: Implement your strategy logic here using pandas
    # Example:
    # df = add_technical_indicators(df)
    # df['SMA20'] = df['Close'].rolling(window=20).mean()
    # df['SMA50'] = df['Close'].rolling(window=50).mean()
    #
    # for i in range(1, len(df)):
    #     if df['SMA20'].iloc[i] > df['SMA50'].iloc[i] and df['SMA20'].iloc[i-1] <= df['SMA50'].iloc[i-1]:
    #         df.loc[df.index[i], "signal"] = 1  # Buy signal
    #     elif df['SMA20'].iloc[i] < df['SMA50'].iloc[i] and df['SMA20'].iloc[i-1] >= df['SMA50'].iloc[i-1]:
    #         df.loc[df.index[i], "signal"] = -1  # Sell signal

    return df


# All specific strategy implementations have been removed as they were unprofitable.
# Use the strategy_template_pandas function above to implement your own profitable strategies.
